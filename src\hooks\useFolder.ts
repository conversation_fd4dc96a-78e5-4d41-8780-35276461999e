import { useAppSelector } from "@/redux/store"
import { useEffect, useState } from "react"
import { useMutationData } from "./useMutationData"
import { getWorkspaceFolders, moveVideoLoaction } from "@/app/actions/workspace"
import useZodForm from "./useZodForm"
import { moveVideoSchema } from "@/components/form/change-video-loaction/schema"

export const useMoveVideos = (videoId: string, currentWorkspace: string,) => {
    //get state redux

    const { folders } = useAppSelector((state) => state.FolderReducer)
    const { workspaces } = useAppSelector((state) => state.WorkspaceReducer)
    //fetching states

    const [isFetching, setIsFetching] = useState(false)
    //state folder

    const [isFolders, setIsFolders] = useState<| ({ _count: { videos: number } } & {
        id: string
        name: string
        createdAt: Date
        updatedAt: Date
        workspaceId: string | null
    })[]
        | undefined>(undefined)

    //usemutation data optimic
    const { mutate, isPending } = useMutationData(
        ['change-video-location'],
        (data: { folder_id: string, workspace_id: string }) => {
            console.log('Form data:', data)
            console.log('Video ID:', videoId)
            return moveVideoLoaction(videoId, data.workspace_id, data.folder_id)
        },
        ['workspace-folders', 'folder-videos', 'user-videos', 'folder-info']
    )

    //usezodfrom
    const { errors, onFormSubmit, watch, register } = useZodForm(moveVideoSchema, mutate, {
        folder_id: '',
        workspace_id: currentWorkspace,
    })

    //fetchfolder with a use effect
    const fetchFolders = async (workspace: string) => {
        setIsFetching(true)
        const folders = await getWorkspaceFolders(workspace)
        setIsFetching(false)
        setIsFolders(folders.data)
    }

    // Watch for workspace changes
    const watchWorkspace = watch('workspace_id')

    useEffect(() => {
        fetchFolders(currentWorkspace)
    }, [])

    useEffect(() => {
        if (watchWorkspace && watchWorkspace !== currentWorkspace) {
            fetchFolders(watchWorkspace)
        }
    }, [watchWorkspace])

    useEffect(() => {
        const workspace = watch(async (value) => {
            if (value.workspace_id)
                fetchFolders(value.workspace_id)
        })
        return () => workspace.unsubscribe()
    }, [watch])

    return {
        errors,
        onFormSubmit,
        register,
        isPending,
        isFetching,
        isFolders,
        workspaces,
        folders,
    }

}