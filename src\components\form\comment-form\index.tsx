
'use client'
import FormGenerator from '@/components/globle/form-generator'
import Loader from '@/components/globle/loader'
import { Button } from '@/components/ui/button'
import { useVideosComment } from '@/hooks/useVideos'
import { Send, X } from 'lucide-react'
import React from 'react'

type Props = {
    videoId: string
    commentId?: string
    author: string
    close?: () => void
}

const CommentForm = ({author, videoId, commentId, close}: Props) => {
    const {errors, onFormSubmit, register, isPending} =
     useVideosComment(videoId, commentId ?? '')


  return (
    <form className='relative w-full' onSubmit={onFormSubmit}>
        <FormGenerator
        inputType='textarea'
        label='Comment'
        placeholder={`Respond to ${author}...`}
        register={register}
        name='content'
        errors={errors}
        lines={8}
        type='text'
        />
        <Button
            className='absolute bottom-3 right-3 p-2 bg-blue-600 hover:bg-blue-700 rounded-full transition-colors duration-200 disabled:opacity-50'
            type='submit'
            disabled={isPending}
        >
            <Loader state={isPending}>
                <Send className='text-white' size={16}/>
            </Loader>
        </Button>

    </form>
  )
}

export default CommentForm