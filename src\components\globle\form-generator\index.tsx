import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import React from 'react'
import { FieldErrors, FieldValues, UseFormRegister } from 'react-hook-form'
import { ErrorMessage } from '@hookform/error-message'
import { Textarea } from '@/components/ui/textarea'

type Props = {
    inputType: 'select' | 'input' | 'textarea'
    type?: 'text' | 'email' | 'password' | 'number' 
    options?: {value: string; label: string; id: string}[]
    label?:string
    placeholder: string
    register: UseFormRegister<any>
    name: string
    errors: FieldErrors<FieldValues>
    lines?: number
}

const FormGenerator = ({
    inputType,
    type,
    options,
    label,
    placeholder,
    register,
    name,
    errors,
    lines,
}: Props) => {
  switch (inputType) {
    case 'input':
        return (
            <Label className='flex flex-col gap-2 text-[#9D9D9D]'
            htmlFor={`input-${label}`} 
            >
                {label && label}
                <Input 
                id={`input-${label}`}
                type={type}
                placeholder={placeholder}
                className='bg-transparent border-themeGray text-themeTextGray'
                {...register(name)} />

                <ErrorMessage 
                name={name}
                 errors={errors} 
                 render={({message}) =>
                 <p className='text-red-400 mt-2'>
                        {message === 'Required' ? '': message}
                        </p>}
                />
            </Label>
        )
    
    case 'select':
        return (
            <Label className='flex flex-col gap-2 text-[#9D9D9D]'
            htmlFor={`select-${label}`} 
            >
                {label && label}
                <select
                id={`select-${label}`}
                className='bg-transparent border-themeGray text-themeTextGray'
                {...register(name)}
                >
                    { options?.length &&
                    options?.map((option) => (
                        <option key={option.id} value={option.value}>
                            {option.label}
                        </option>
                    ))}
                </select>

                <ErrorMessage 
                name={name}
                 errors={errors} 
                 render={({message}) =>
                 <p className='text-red-400 mt-2'>
                        {message === 'Required' ? '': message}
                        </p>}
                />
            </Label>
        )

        case 'textarea':
        return (
            <Label className='flex flex-col gap-2 text-[#9D9D9D]'
            htmlFor={`input-${label}`} 
            >
                {label && label}
                <Textarea
                id={`textarea-${label}`}
                className='bg-transparent border-themeGray text-themeTextGray'
                placeholder={placeholder}
                rows={lines}
                {...register(name)}
                />
                <ErrorMessage 
                name={name}
                 errors={errors} 
                 render={({message}) =>
                 <p className='text-red-400 mt-2'>
                        {message === 'Required' ? '': message}
                        </p>}
                />
            </Label>
        )

    default:
        break;
  }
}

export default FormGenerator