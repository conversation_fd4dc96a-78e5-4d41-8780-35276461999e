import { TabsContent } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import React, { useState, useEffect } from 'react'
import { Activity, Eye, MessageCircle, Share2, ThumbsUp, Clock, User, TrendingUp } from 'lucide-react'
import { toast } from 'sonner'

type Props = {
    videoId: string
}

type ActivityItem = {
    id: string
    type: 'view' | 'like' | 'comment' | 'share'
    user: {
        name: string
        avatar?: string
    }
    timestamp: Date
    details?: string
}

const VideoActivity = ({ videoId }: Props) => {
    const [activities, setActivities] = useState<ActivityItem[]>([])
    const [isLoading, setIsLoading] = useState(true)
    const [stats, setStats] = useState({
        totalViews: 0,
        totalLikes: 0,
        totalComments: 0,
        totalShares: 0
    })

    useEffect(() => {
        // Simulate loading activity data
        const loadActivityData = async () => {
            setIsLoading(true)
            try {
                // Simulate API call
                await new Promise(resolve => setTimeout(resolve, 1000))
                
                // Mock activity data
                const mockActivities: ActivityItem[] = [
                    {
                        id: '1',
                        type: 'view',
                        user: { name: '<PERSON> Do<PERSON>' },
                        timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
                    },
                    {
                        id: '2',
                        type: 'like',
                        user: { name: 'Sarah Wilson' },
                        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
                    },
                    {
                        id: '3',
                        type: 'comment',
                        user: { name: 'Mike Johnson' },
                        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 4), // 4 hours ago
                        details: 'Great explanation! This really helped me understand the concept.'
                    },
                    {
                        id: '4',
                        type: 'share',
                        user: { name: 'Emily Chen' },
                        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 6), // 6 hours ago
                    },
                    {
                        id: '5',
                        type: 'view',
                        user: { name: 'David Brown' },
                        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 8), // 8 hours ago
                    }
                ]

                setActivities(mockActivities)
                setStats({
                    totalViews: 156,
                    totalLikes: 23,
                    totalComments: 8,
                    totalShares: 12
                })
            } catch (error) {
                toast.error('Failed to load activity data')
            } finally {
                setIsLoading(false)
            }
        }

        loadActivityData()
    }, [videoId])

    const getActivityIcon = (type: string) => {
        switch (type) {
            case 'view':
                return <Eye className='w-4 h-4 text-blue-400' />
            case 'like':
                return <ThumbsUp className='w-4 h-4 text-red-400' />
            case 'comment':
                return <MessageCircle className='w-4 h-4 text-green-400' />
            case 'share':
                return <Share2 className='w-4 h-4 text-purple-400' />
            default:
                return <Activity className='w-4 h-4 text-gray-400' />
        }
    }

    const getActivityText = (activity: ActivityItem) => {
        switch (activity.type) {
            case 'view':
                return 'viewed this video'
            case 'like':
                return 'liked this video'
            case 'comment':
                return 'commented on this video'
            case 'share':
                return 'shared this video'
            default:
                return 'interacted with this video'
        }
    }

    const formatTimeAgo = (timestamp: Date) => {
        const now = new Date()
        const diffInMinutes = Math.floor((now.getTime() - timestamp.getTime()) / (1000 * 60))
        
        if (diffInMinutes < 60) {
            return `${diffInMinutes}m ago`
        } else if (diffInMinutes < 1440) {
            return `${Math.floor(diffInMinutes / 60)}h ago`
        } else {
            return `${Math.floor(diffInMinutes / 1440)}d ago`
        }
    }

    return (
        <TabsContent value='Activity' className='p-6 space-y-6'>
            {/* Header */}
            <div className='flex items-center gap-3'>
                <TrendingUp className='w-5 h-5 text-purple-400' />
                <h3 className='text-lg font-semibold text-white'>Video Activity</h3>
            </div>

            {/* Stats Overview */}
            <div className='grid grid-cols-2 gap-4'>
                <div className='bg-[#1A1A1A] rounded-lg border border-[#2A2A2A] p-4'>
                    <div className='flex items-center gap-3'>
                        <Eye className='w-5 h-5 text-blue-400' />
                        <div>
                            <p className='text-2xl font-bold text-white'>{stats.totalViews}</p>
                            <p className='text-sm text-gray-400'>Views</p>
                        </div>
                    </div>
                </div>
                <div className='bg-[#1A1A1A] rounded-lg border border-[#2A2A2A] p-4'>
                    <div className='flex items-center gap-3'>
                        <ThumbsUp className='w-5 h-5 text-red-400' />
                        <div>
                            <p className='text-2xl font-bold text-white'>{stats.totalLikes}</p>
                            <p className='text-sm text-gray-400'>Likes</p>
                        </div>
                    </div>
                </div>
                <div className='bg-[#1A1A1A] rounded-lg border border-[#2A2A2A] p-4'>
                    <div className='flex items-center gap-3'>
                        <MessageCircle className='w-5 h-5 text-green-400' />
                        <div>
                            <p className='text-2xl font-bold text-white'>{stats.totalComments}</p>
                            <p className='text-sm text-gray-400'>Comments</p>
                        </div>
                    </div>
                </div>
                <div className='bg-[#1A1A1A] rounded-lg border border-[#2A2A2A] p-4'>
                    <div className='flex items-center gap-3'>
                        <Share2 className='w-5 h-5 text-purple-400' />
                        <div>
                            <p className='text-2xl font-bold text-white'>{stats.totalShares}</p>
                            <p className='text-sm text-gray-400'>Shares</p>
                        </div>
                    </div>
                </div>
            </div>

            {/* Recent Activity */}
            <div className='space-y-4'>
                <h4 className='text-md font-semibold text-white flex items-center gap-2'>
                    <Clock className='w-4 h-4' />
                    Recent Activity
                </h4>

                {isLoading ? (
                    <div className='space-y-3'>
                        {[...Array(5)].map((_, i) => (
                            <div key={i} className='bg-[#1A1A1A] rounded-lg border border-[#2A2A2A] p-4 animate-pulse'>
                                <div className='flex items-start gap-3'>
                                    <div className='w-8 h-8 bg-gray-700 rounded-full'></div>
                                    <div className='flex-1 space-y-2'>
                                        <div className='h-4 bg-gray-700 rounded w-3/4'></div>
                                        <div className='h-3 bg-gray-700 rounded w-1/2'></div>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                ) : activities.length > 0 ? (
                    <div className='space-y-3 max-h-96 overflow-y-auto'>
                        {activities.map((activity) => (
                            <div key={activity.id} className='bg-[#1A1A1A] rounded-lg border border-[#2A2A2A] p-4 hover:bg-[#1F1F1F] transition-colors'>
                                <div className='flex items-start gap-3'>
                                    <div className='w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-white font-semibold text-xs'>
                                        {activity.user.name.charAt(0)}
                                    </div>
                                    <div className='flex-1'>
                                        <div className='flex items-center gap-2 mb-1'>
                                            {getActivityIcon(activity.type)}
                                            <span className='text-white font-medium'>{activity.user.name}</span>
                                            <span className='text-gray-400 text-sm'>{getActivityText(activity)}</span>
                                        </div>
                                        {activity.details && (
                                            <p className='text-gray-300 text-sm mt-2 bg-[#0F0F0F] rounded p-2'>
                                                "{activity.details}"
                                            </p>
                                        )}
                                        <p className='text-gray-500 text-xs mt-2'>{formatTimeAgo(activity.timestamp)}</p>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                ) : (
                    <div className='text-center py-8'>
                        <Activity className='w-12 h-12 text-gray-600 mx-auto mb-3' />
                        <p className='text-gray-400'>No activity yet</p>
                        <p className='text-sm text-gray-500 mt-1'>Activity will appear here as people interact with your video</p>
                    </div>
                )}
            </div>
        </TabsContent>
    )
}

export default VideoActivity
