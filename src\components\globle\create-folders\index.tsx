'use client'
import { SidebarFolderIcon } from '@/components/icons/SidebarIcons'
import { Button } from '@/components/ui/button'
import { useCreateFolders } from '@/hooks/useCreateFolders'
import React from 'react'

type Props = {
    workspaceId: string
}

const CreateFolders = ({workspaceId}: Props) => {

  const {onCreateNewFolder} = useCreateFolders(workspaceId)


  return (
    <Button onClick={onCreateNewFolder} className='bg-[#1D1D1D] text-[#707070] flex items-center gap-2 py-6 px-4 rounded-2xl'>
      <SidebarFolderIcon size={22} color='#ffffff' />
      Create a folder
    </Button>
  )
}

export default CreateFolders