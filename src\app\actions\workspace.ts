"use server"

import {prisma as client} from '@/lib/prisma'
import { currentUser } from "@clerk/nextjs/server"
import { sendEmail } from './user'



export const verifyAccessToWorkspace = async (workspaceId: string) => {
    try{
        const user = await currentUser()
        if(!user){
            return {status: 403}
        }
        const isUserInWorkspace = await client.workspace.findUnique({
           where: {
            id: workspaceId,
            OR: [
                {
                    User:{
                        clerkId: user.id,
                    },
                },
                {
                    members: {
                        every: {
                            User:{
                                clerkId: user.id
                            }
                        }
                    }
                }
            ]
           }
    })
    return {
        status: 200,
        data:{workspace: isUserInWorkspace},
    }
}
catch(error){
    return{
        status: 403,
        data: {workspace: null}
    }
}
}

export const getWorkspaceFolders = async (workspaceId: string) => {
    try{
        const folders = await client.folder.findMany({
            where: {
                workspaceId: workspaceId,
            },
            select: {
                id: true,
                name: true,
                createdAt: true,
                workspaceId: true
            }
        })

        // Add video counts to each folder
        const foldersWithCounts = await Promise.all(
            folders.map(async (folder) => {
                const videoCount = await client.video.count({
                    where: { folderId: folder.id }
                })
                return {
                    ...folder,
                    _count: {
                        videos: videoCount
                    }
                }
            })
        )

        if(foldersWithCounts && foldersWithCounts.length > 0){
            return {status: 200, data: foldersWithCounts}
        }

        return {status: 404, data:[]}
    }
    catch(error){
        console.error('Get workspace folders error:', error)
        return {status: 403, data:[]}
    }
}

export const getAllUserVideos = async (workspaceId: string) => {
    try{
        const user = await currentUser()
        if(!user) return {status:404, data: []}
        const videos = await client.video.findMany({
            where: {
                workspaceId: workspaceId,
                folderId: null, // Only videos in workspace root (not in folders)
            },
            select: {
                id:true,
                title: true,
                createdAt: true,
                source: true,
                User: {
                    select: {
                        firstName: true,
                        lastName: true,
                        image: true,
                    }
                },
                folder: {
                    select: {
                        id: true,
                        name: true,
                    }
                }
            },
            orderBy: {
                createdAt: 'asc'
            },
        })

        if(videos && videos.length > 0){
            return {status: 200, data: videos}
        }
        return {status: 404, data: []}

    } catch(error){
        console.error('Get all user videos error:', error)
        return {status: 400, data: []}
        }}

export const getFolderVideos = async (folderId: string) => {
    try{
        const user = await currentUser()
        if(!user) return {status:404, data: []}
        const videos = await client.video.findMany({
            where: {
                folderId: folderId, // Videos in specific folder
            },
            select: {
                id:true,
                title: true,
                createdAt: true,
                source: true,
                User: {
                    select: {
                        firstName: true,
                        lastName: true,
                        image: true,
                    }
                },
                folder: {
                    select: {
                        id: true,
                        name: true,
                    }
                }
            },
            orderBy: {
                createdAt: 'asc'
            },
        })

        if(videos && videos.length > 0){
            return {status: 200, data: videos}
        }
        return {status: 404, data: []}

    } catch(error){
        console.error('Get folder videos error:', error)
        return {status: 400, data: []}
        }}


export const getWorkspaces = async () => {
    try{
        const user = await currentUser()
        if(!user) return {status: 404}
        const workspaces = await client.workspace.findMany({
            where: {
                User: {
                    clerkId: user.id,
                }
            },
            select: {
                id: true,
                name: true,
                type: true,
                User: {
                    select: {
                        subscription: {
                            select:{
                                plan: true,
                            }
                        }
                    }
                },
                members: {
                    select: {
                        id: true,
                        User: {
                            select: {
                                id: true,
                                firstName: true,
                                lastName: true,
                                email: true,
                                image: true
                            }
                        }
                    }
                }
            }
        })
        if(workspaces){
            return {status: 200, data: workspaces}
        }
        return {status: 404, data: []}
    }
    catch(error){
        console.error('Get workspaces error:', error)
        return {status: 400, data: []}
    }
}

export const createWorkspace = async (name: string) => {
    try {
        const user = await currentUser()
        if (!user) return { status: 404, message: 'User not found' }

        // Get user data including current workspaces and subscription
        const userData = await client.user.findUnique({
            where: {
                clerkId: user.id,
            },
            select: {
                id: true,
                subscription: {
                    select: {
                        plan: true,
                    },
                },
                Workspace: {
                    select: {
                        id: true,
                        type: true,
                    }
                }
            },
        })

        if (!userData) return { status: 404, message: 'User not found in database' }

        const userPlan = userData.subscription?.plan || 'FREE'
        const currentWorkspaces = userData.Workspace || []

        // Check workspace limits
        if (userPlan === 'FREE' && currentWorkspaces.length >= 2) {
            return {
                status: 401,
                message: 'Free users can only create 2 workspaces. Upgrade to PRO for unlimited workspaces.'
            }
        }

        // Create the workspace
        const workspace = await client.workspace.create({
            data: {
                name: name || 'Untitled Workspace',
                type: "PUBLIC",
                userId: userData.id,
            },
            select: {
                id: true,
                name: true,
                type: true,
            }
        })

        if (workspace) {
            return { status: 201, data: workspace, message: 'Workspace created successfully' }
        }

        return { status: 400, message: 'Failed to create workspace' }
    } catch (error) {
        console.error('Create workspace error:', error)
        return { status: 500, message: 'Internal server error' }
    }
}

export const createFolder = async (workspaceId: string, name: string) => {
    try {
        const user = await currentUser()
        if (!user) return { status: 401, message: 'Unauthorized' }

        const folder = await client.folder.create({
            data: {
                name,
                workspaceId
            }
        })

        if (folder) {
            return { status: 200, data: folder, message: 'Folder created successfully' }
        }

        return { status: 400, message: 'Failed to create folder' }
    } catch (error) {
        console.error('Create folder error:', error)
        return { status: 500, message: 'Internal server error' }
    }
}

export const renamedFolders = async (folderId: string, name: string) => {
    try {
        const user = await currentUser()
        if (!user) return { status: 401, message: 'Unauthorized' }

        const folder = await client.folder.update({
            where: { id: folderId },
            data: { name }
        })

        if (folder) {
            return { status: 200, data: folder, message: 'Folder renamed successfully' }
        }

        return { status: 400, message: 'Failed to rename folder' }
    } catch (error) {
        console.error('Rename folder error:', error)
        return { status: 500, message: 'Internal server error' }
    }
}


export const getFolderInfo = async (folderId: string) => {
    try {
        const folder = await client.folder.findUnique({
            where: { id: folderId },
            select: {
                name: true,
            }
        })

        if (!folder) {
            return { status: 400, message: 'Folder not found' }
        }

        // Get video count separately
        const videoCount = await client.video.count({
            where: { folderId: folderId }
        })

        const folderWithCount = {
            name: folder.name,
            _count: {
                videos: videoCount
            }
        }

        return { status: 200, data: folderWithCount }
    } catch (error) {
        console.error('Get folder info error:', error)
        return { status: 500, message: 'Internal server error' }
    }
}

export const moveVideoLoaction = async (videoId: string, workspaceId: string, folderId?: string) => {
    try {
        console.log('Moving video:', { videoId, workspaceId, folderId })

        if (!videoId || !workspaceId) {
            return { status: 400, message: 'Video ID and Workspace ID are required' }
        }

        const location = await client.video.update({
            where: { id: videoId },
            data: {
                 folderId: folderId || null ,
                 workspaceId
                }
        })

        if (location) {
            return { status: 200, message: 'Video moved successfully' }
        }

        return { status: 400, message: 'Failed to move video' }
    } catch (error) {
        console.error('Move video error:', error)
        return { status: 500, message: 'Internal server error' }
    }
}

export const createVideo = async (
    workspaceId: string,
    title: string,
    source: string,
    folderId?: string
) => {
    try {
        const user = await currentUser()
        if (!user) return { status: 404, message: 'User not found' }

        // Get the database user ID
        const dbUser = await client.user.findUnique({
            where: { clerkId: user.id },
            select: { id: true }
        })

        if (!dbUser) return { status: 404, message: 'Database user not found' }

        const video = await client.video.create({
            data: {
                title: title || 'Untitled Video',
                name: title || 'Untitled Video',
                source: source,
                workspaceId: workspaceId,
                folderId: folderId || null,
                userId: dbUser.id,
            },
            select: {
                id: true,
                title: true,
                createdAt: true,
                source: true,
                User: {
                    select: {
                        firstName: true,
                        lastName: true,
                        image: true,
                    }
                },
                folder: {
                    select: {
                        id: true,
                        name: true,
                    }
                }
            }
        })

        if (video) {
            return { status: 200, data: video, message: 'Video created successfully' }
        }

        return { status: 400, message: 'Failed to create video' }
    } catch (error) {
        console.error('Create video error:', error)
        return { status: 500, message: 'Internal server error' }
    }
}

export const getPreviewVideo = async (videoId: string) => {
    try {
        const user = await currentUser()
        if (!user) return { status: 404, data: null }

        const video = await client.video.findUnique({
            where: { id: videoId },
            select: {
                id: true,
                title: true,
                description: true,
                source: true,
                createdAt: true,
                views: true,
                summery: true,
                User: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        image: true,
                        trial: true,
                        subscription: {
                            select: {
                                plan: true,
                            }
                        }
                    }
                },
                folder: {
                    select: {
                        id: true,
                        name: true,
                    }
                },
                Workspace: {
                    select: {
                        id: true,
                        name: true,
                    }
                }
            }
        })

        if (video) {
            // Check if current user is the author
            const dbUser = await client.user.findUnique({
                where: { clerkId: user.id },
                select: { id: true }
            })

            const isAuthor = dbUser && video.User && dbUser.id === video.User.id

            return {
                status: 200,
                data: video,
                author: isAuthor
            }
        }

        return { status: 404, data: null }
    } catch (error) {
        console.error('Get preview video error:', error)
        return { status: 500, data: null }
    }
}

export const sendEamilForFirstView = async (videoId: string) => {
    try {
        const user = await currentUser()
        if (!user) return { status: 404, }
        const firstViewSettings = await client.user.findUnique({
            where: {
                clerkId: user.id,
            },
            select: {
                fistview: true,
            },
        })
       if(!firstViewSettings?.fistview) return 
       const video = await client.video.findUnique({
        where: {
            id: videoId,
        },
        select:{
            title: true,
            views: true,
            User: {
                select: {
                    email: true
                }
            }
        }
       })

       if(video && video.views === 0){

        await client.video.update({
            where: {
                id: videoId,
            },
            data: {
                views: video.views + 1,
            }
        })
       }

       if(!video) return
       const {transporter, mailOptions} = await sendEmail(
            video.User?.email!,
            'Your video has been viewed',
            `Your video ${video?.title} has been viewed`,
        )

        transporter.sendMail(mailOptions, async (error, info) => {
            if (error) {
                console.error('Email error:', error);
            }else{
                const notification = await client.user.update({
                    where: {
                        clerkId: user.id,
                    },
                    data: {
                        notification: {
                            create: {
                                content: mailOptions.text,
                            }
                        },
                    }
                })
                if(notification){
                    return {status: 200,}
                }
            }
        });

    }catch (error) {
        console.error('Send email error:', error)
        return { status: 500, data: null }
    }
}
