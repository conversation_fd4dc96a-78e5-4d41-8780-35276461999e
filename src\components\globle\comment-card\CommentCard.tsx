'use client'
import CommentForm from '@/components/form/comment-form';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { CommentRepliesProps } from '@/types/index.type';
import React, { useState } from 'react'

type Props = {
    comment: string
    author: {image: string | null; firstName: string | null; lastName: string | null}
    createdAt?: string
    reply: CommentRepliesProps[]
    videoId: string
    isReply?: boolean
    commentId?: string
}

const CommentCard = ({author, 
    comment, createdAt, reply, 
    videoId, isReply, commentId}: Props) => {

        const [onReply, setOnReply] = useState<boolean>(false)

  return (
    <Card 
    className={cn(
        isReply ? 'bg-[#1D1D1D] pl-10 border-none' 
        : 'border-[1px] border-[#1D1D1D] p-5'
    )}>
        <div className='flex items-center gap-x-2'>
        <Avatar className='w-6 h-6'>
            <AvatarImage
                src={author.image || '/default-avatar.png'}
                alt='author'
            />
            <AvatarFallback className='text-xs'>
                {author.firstName?.charAt(0) || author.lastName?.charAt(0) || 'U'}
            </AvatarFallback>
        </Avatar>
        <p className='capatialize text-sm text-[#BDBDBD]'>
            {author.firstName || 'Anonymous'} {author.lastName || ''}
        </p>
        </div>
        <div>
            <p className='text-[#BDBDBD]'>
                {comment}
            </p>
        </div>
        {!isReply && (
        <div className='flex justify-end mt-3'>
            {!onReply ? (
                <Button onClick={() => setOnReply(true)} className='text-sm rounded-full bg-[#252525] text-white hover:text-black'>
                    Reply
                </Button>
            ): (
                <CommentForm  
                close={() => setOnReply(false)}
                author={`${author.firstName + '' +author.lastName}`}
                videoId={videoId}
                commentId={commentId}
                />

            )}
        </div>
        )}

        {reply.length > 0 && (
            <div className='flex flex-col gap-y-10 mt-5'>
            {reply.map((r) => (
                <CommentCard 
                isReply
                reply={[]}
                comment={r.content}
                videoId={videoId}
                commentId={r.commentId!}
                key={r.id}
                author={{
                    image: r.User?.image!,
                    firstName: r.User?.firstName!,
                    lastName: r.User?.lastName!
                }}
                />
            ))}                 
            </div>
        )}
    </Card>
  )
}

export default CommentCard