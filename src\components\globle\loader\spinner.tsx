type Props = {
  color?: string;
};

export const Spinner = ({ color}: Props) => {
  return (
    <div role="status">
      <svg
        aria-hidden="true"
        className="inline w-8 h-8 text-gray-200 animate-spin dark:text-gray-600"
        viewBox="0 0 100 101"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 
          100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 
          0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08157 
          50.5908C9.08157 73.1865 27.4043 91.5093 50 91.5093C72.5957 
          91.5093 90.9184 73.1865 90.9184 50.5908C90.9184 27.9951 72.5957 
          9.67236 50 9.67236C27.4043 9.67236 9.08157 27.9951 9.08157 
          50.5908Z"
          fill={color || '#fff'}
        />
        <path
          d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 
          97.0079 33.5532C95.2932 28.8227 92.871 24.3692 
          89.8167 20.348C85.8452 15.1192 80.8826 10.7236 
          75.2124 7.41289C69.5422 4.10222 63.2754 1.94025 
          56.7698 1.05124C51.7666 0.367363 46.6976 
          0.446843 41.7345 1.27873C39.2611 1.69328 
          37.813 4.19778 38.4501 6.62326C39.0873 
          9.04874 41.5694 10.4717 44.0505 
          10.1071C47.8511 9.54855 51.7191 9.52689 
          55.5402 10.0491C60.864 10.7766 65.9927 
          12.5457 70.6331 15.2552C75.2735 17.9648 
          79.3235 21.5619 82.5886 26.0176C84.9175 
          29.1486 86.7997 32.6232 88.1811 
          36.3232C89.083 38.6816 91.5421 
          39.6781 93.9676 39.0409Z"
          fill={color || '#fff'}
        />
      </svg>
      <span className="sr-only">Loading...</span>
    </div>
  );
};
