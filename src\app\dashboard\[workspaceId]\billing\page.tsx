import { getPaymentInfo } from '@/app/actions/user'
import React from 'react'

type Props = {

}

const BillingPage = async (props: Props) => {

    const payment = await getPaymentInfo()
  return (
    <div className='bg-[#1D1D1D] flex flex-col gap-y-8 p-5 rounded-xl'>
        <div>
            <h2 className='text-2xl'>Current Plan</h2>
            <p className='text-[#9D9D9D]'>Your payment Histroy</p>
        </div>
        <div>
            <h2 className='text-2xl'>
                ${payment.data?.subscription?.plan === 'PRO' ? '19.99' : '0.00'}/Month
            </h2>
            <p className='text-[#9D9D9D]'>
                {payment?.data?.subscription?.plan === 'PRO' ? 'Pro Plan' : 'Free Plan'}
            </p>
        </div>
        
    </div>
  )
}

export default BillingPage