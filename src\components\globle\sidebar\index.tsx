'use client'
import {  getWorkspaces } from '@/app/actions/workspace'
import { Select, SelectContent, SelectGroup, SelectItem, SelectLabel, SelectTrigger, SelectValue,  } from '@/components/ui/select'
import { Separator } from '@/components/ui/separator'
import { useQueryData } from '@/hooks/useQueryData'
import { NotificationProps, WorkspaceProps } from '@/types/index.type'
import Image from 'next/image'
import { usePathname, useRouter } from 'next/navigation'
import React from 'react'
import Modal from '@/components/globle/modal'
import { Menu, PlusCircle } from 'lucide-react'
import Search from '../search'
import { MENU_ITEMS } from '@/constants'
import SidebarItem from './sidebar-items'
import { getNotifications } from '@/app/actions/user'
import WorkspacePlaceholder from './workspace-placeholder'
import GlobalCard from '../globle-card'
import Loader from '../loader'
import { Button } from '@/components/ui/button'
import { Sheet, She<PERSON><PERSON>ontent, She<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>it<PERSON> } from '@/components/ui/sheet'
import { VisuallyH<PERSON>den } from '@/components/ui/visually-hidden'
import InfoBar from '../info-bar'
import {useDispatch} from 'react-redux'
import { WORKSPACES } from '@/redux/slices/workspaces'
import PaymentButton from '../paymentbutton'


type Props = {
    activeWorkspaceId: string
}

const Sidebar = ({activeWorkspaceId}: Props) => {
  const router = useRouter()
  const pathName = usePathname()
  const dispatch = useDispatch()

  const {data, isFetched} = useQueryData(["user-workspaces"], 
    getWorkspaces)

    const menuItems = MENU_ITEMS(activeWorkspaceId)

    const {data: notifications} = useQueryData(['user-notifications'], getNotifications)

  const workspaceData = data as WorkspaceProps
  const workspace = workspaceData?.data || []

  const notificationData = notifications as NotificationProps
  const count = notificationData?.data


  const onChangeActiveWorkspace = (value: string) => {
    //redirect to the workspace
    router.push(`/dashboard/${value}`)
  }

  const currentWorkspace = workspace.find((s) =>
     s.id===activeWorkspaceId)

  if(isFetched && workspace){
    dispatch(WORKSPACES({workspaces: workspace}))
  }

  
  const SidebarSection= (
    <div className='bg-[#111111] flex-none relative h-full w-[250px] flex flex-col gap-4 overflow-hidden'>
      <div className='bg-[#111111] p-4 gap-2 flex justify-center items-center flex-shrink-0 border-b border-[#333]'>
    <Image
    src='/lomm.svg'
    height={40}
    width={40}
    alt='logo'
    />
    <p className='text-2xl'>Lomm</p>
      </div>
      <div className='p-4 flex-shrink-0'>
        <Select defaultValue={activeWorkspaceId}
         onValueChange={onChangeActiveWorkspace}
         >
          <SelectTrigger className='text-neutral-400 bg-transparent'>
            <SelectValue placeholder="Select a workspace" className='text-neutral-400' >Select a workspace</SelectValue>
          </SelectTrigger>
        <SelectContent className='bg-[#111111] backdrop-blur-xl'>
          <SelectGroup>
            <SelectLabel>
              Workspaces
            </SelectLabel>
            <Separator/>

          { workspace.map((workspace) => (
          <SelectItem key={workspace.id} value={workspace.id}>
           {workspace.name}
          </SelectItem>
           )
          )}

          
          {currentWorkspace?.members && currentWorkspace.members.length > 0 && 
          currentWorkspace.members.map((workspace) => workspace.WorkSpace &&  (
            <SelectItem 
            key={workspace.WorkSpace.id}
             value={workspace.WorkSpace.id}>
            {/* {member.User?.firstName || member.User?.lastName || member.User?.email} */}
            {workspace.WorkSpace.name}
            </SelectItem>
          )
          )}

          </SelectGroup>
        </SelectContent>
       </Select>
      </div>

      <div className='flex-1 overflow-y-auto px-4'>
       { currentWorkspace?.type === "PUBLIC" &&
       currentWorkspace.User?.subscription?.plan === 'PRO' && ( <Modal trigger={
          <span className='text-sm cursor-pointer flex items-center justify-center bg-neutral-800/90 hover:bg-neutral-800/60 w-full rounded-sm p-[5px] gap-2'>
            <PlusCircle size={15} className='text-neutral-800/90 fill-neutral-500' />
            <span className='text-neutral-400 font-semibold text-xs'>
              Invite To Workspace
            </span>
          </span>
        } title="Invite To Workspace"
        description='Invite your friends to your workspace'>
          {/* <div className='p-4'>
            <p>Invite members to collaborate in your workspace</p>
          </div> */}
       <div className='px-2 flex flex-col gap-4 overflow-y-auto'>
         <Search workspaceId={activeWorkspaceId} />
       </div>
        </Modal>)}
        <p className='w-full text-[#909D9D] font-bold mt-4'>Menu</p>
        <nav className='w-full'>
          <ul className='space-y-1'>
            {menuItems.map((item) => (
            <SidebarItem
            href={item.href}
            icon={item.icon}
            title={item.title}
            selected={pathName === item.href}
              key={item.title}
              notifications={
                item.title === 'Notification'
                  ? count?._count?.notification || 0
                  : undefined
               }
            />
          ))}</ul>
          
        </nav>
        
        <Separator className='w-4/5 my-4'/>

        <p className='w-full text-[#909D9D] font-bold mb-2'>Workspaces</p>
       {
            workspace.length === 1 &&
             currentWorkspace && (!currentWorkspace.members || currentWorkspace.members.length === 0) && (
              <div className='w-full mt-[-10px]'>
                <p className=' text-[#3c3c3c] font-medium text-sm'>
              {currentWorkspace.User?.subscription?.plan === 'FREE' ? 'Upgrade to create workspace' : 'No Workspaces'}
                </p>
              </div>
             )
          }

        <nav className='w-full'>
          <ul className='space-y-1 max-h-[150px] overflow-y-auto'>
          {workspace.length > 0 &&
          workspace.map((item) =>
            item.type !== 'PRIVATE' && (
            <SidebarItem
            href={`/dashboard/${item.id}`}
            title={item.name}
            selected={pathName === `/dashboard/${item.id}`}
            key={item.name}
            notifications={undefined}
            icon={
            <WorkspacePlaceholder>{item.name.charAt(0)}</WorkspacePlaceholder>
          }
            />

          )
          )}
          </ul>
        </nav>
        
        <Separator className='w-4/5 my-4'/>

        {currentWorkspace?.members && currentWorkspace.members.length > 0 && (
          <>
            <p className='w-full text-[#909D9D] font-bold mb-2'>Members</p>
            <nav className='w-full'>
              <ul className='space-y-1 max-h-[100px] overflow-y-auto'>
                {currentWorkspace.members.map((item) =>(
                  <SidebarItem
                  href={`/dashboard/${item.WorkSpace.id}`}
                  title={item.WorkSpace.name}
                  selected={pathName  === `/dashboard/${item.WorkSpace.id}`}
                  key={item.WorkSpace.id}
                  notifications={0}
                  icon={
                  <WorkspacePlaceholder>
                    {item.WorkSpace.name.charAt(0)}
                  </WorkspacePlaceholder>
                }
                />
              ))}
              </ul>
            </nav>
          </>
        )}
        </div>
        
        <Separator className='w-4/5'/>

        {/* <div className='mt-auto pt-4'> */}
          {currentWorkspace?.User?.subscription?.plan === 'FREE' && (
         <GlobalCard  
         title='Upgrade to Pro'
         description='Unlock AI like transcription, AI summary, and more'
         footer ={
          <PaymentButton/>
        //  <Button className='text-sm w-full'>
        //   <Loader state={false}>
        //   Upgrade
        //   </Loader>
        //  </Button>
         }
           />
)}
        </div>
        // </div>
  )
  return <div className='full'>
    {/* infobar */}
    <InfoBar/>
    {/* ?sheet mobile and desktop */}
    <div className='md:hidden fixed my-4'>
      <Sheet>
        <SheetTrigger asChild className='mt-[2px]'  >
        <Button variant={'ghost'} className='mt-[2px]'>
        <Menu/>
        </Button>
        </SheetTrigger>
        <SheetContent side={'left'} className='p-0 w-fit h-full'>
          <VisuallyHidden>
            <SheetTitle>Navigation Menu</SheetTitle>
          </VisuallyHidden>
          {SidebarSection}
        </SheetContent>
      </Sheet>
    </div>
    <div className='md:block hidden h-full'>
    {SidebarSection}
    </div>
  </div>
}

export default Sidebar