import { createCommentSchema } from "@/components/form/comment-form/schema"
import { useMutationData } from "./useMutationData"
import { useQueryData } from "./useQueryData"
import useZodForm from "./useZodForm"
import { createCommentAndReply, getUserProfile } from "@/app/actions/user"

export const useVideosComment = (videoId: string , commentId?: string) =>
     {
        const {data} = useQueryData(['user-profile'], getUserProfile)

        const {status , data: user} = data as {
            status: number
            data: {
                id: string
                image: string
            }
        }

        const {isPending, mutate} = useMutationData
        (['new-comment'], (data: {content: string}) =>
            createCommentAndReply(user.id, videoId, data.content, commentId),
            'video-comments'
        )

        const {register, errors, onFormSubmit, reset} = useZodForm(createCommentSchema,
            mutate)

            return {
                register,
                errors,
                onFormSubmit,
                isPending,
                reset
            }
}