import { SignUp } from '@clerk/nextjs'
import { currentUser } from '@clerk/nextjs/server'
import { redirect } from 'next/navigation'

export default async function SignUpPage() {
    const user = await currentUser()

    // If user is already signed in, redirect to dashboard
    if (user) {
        redirect('/dashboard')
    }

    return (
        <div className="flex items-center justify-center min-h-screen">
            <SignUp
                afterSignInUrl="/auth/callback"
                afterSignUpUrl="/auth/callback"
            />
        </div>
    )
}