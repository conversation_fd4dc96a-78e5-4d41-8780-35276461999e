'use client'
import { getAllUserVideos, getFolderVideos, createVideo } from '@/app/actions/workspace'
import { SidebarVideoRecorderIcon } from '@/components/icons/SidebarIcons'
import { useQueryData } from '@/hooks/useQueryData'
import { useMutationData } from '@/hooks/useMutationData'
import { cn } from '@/lib/utils'
import { VideosProps } from '@/types/index.type'
import React, { useState } from 'react'
import VideoCard from './video-card'
import { Button } from '@/components/ui/button'
import { Plus } from 'lucide-react'

type Props = {
    folderId: string
    workspaceId: string
    videosKey: string
}



const Videos = ({videosKey, folderId, workspaceId}: Props) => {
    // add videos logic - use different functions based on videosKey
    const queryFn = videosKey === 'folder-videos'
        ? () => getFolderVideos(folderId)
        : () => getAllUserVideos(workspaceId)

    const {data: videoData} = useQueryData([videosKey], queryFn)

    const {status: videoStatus, data: videos} = videoData as VideosProps

    // Add video mutation
    const { mutate: addVideo, isPending } = useMutationData(
        ['create-video'],
        (data: { title: string, source: string }) =>
            createVideo(workspaceId, data.title, data.source, videosKey === 'folder-videos' ? folderId : undefined),
        [videosKey, 'workspace-folders', 'folder-info', 'user-videos']
    )

    // Add sample video function
    const handleAddSampleVideo = () => {
        const currentLocation = videosKey === 'folder-videos' ? 'folder' : 'workspace'
        const sampleVideos = [
            { title: `Marketing Demo - ${currentLocation}`, source: `sample-marketing-demo-${Date.now()}.mp4` },
            { title: `Product Tutorial - ${currentLocation}`, source: `sample-product-tutorial-${Date.now()}.mp4` },
            { title: `Team Meeting - ${currentLocation}`, source: `sample-team-meeting-${Date.now()}.mp4` },
            { title: `Screen Recording - ${currentLocation}`, source: `sample-screen-recording-${Date.now()}.mp4` },
            { title: `Presentation - ${currentLocation}`, source: `sample-presentation-${Date.now()}.mp4` },
        ]

        const randomVideo = sampleVideos[Math.floor(Math.random() * sampleVideos.length)]
        addVideo(randomVideo)
    }


  return (
    <div className='flex flex-col gap-4 mt-4'>
        <div className='flex items-center justify-between'>
            <div className='flex items-center gap-4'>
                <SidebarVideoRecorderIcon size={22} color='#ffffff' />
                <h2 className='text-[#BdBdBd] text-xl'>Videos</h2>
            </div>
            <Button
                onClick={handleAddSampleVideo}
                disabled={isPending}
                size="sm"
                className='bg-[#9D5CFF] hover:bg-[#8B4FE6] text-white'
            >
                <Plus size={16} className='mr-2' />
                {isPending ? 'Adding...' : 'Add Video'}
            </Button>
        </div>
        <section className={cn(
            videoStatus !== 200 || !videos || videos.length === 0
                ? 'p-5 flex items-center justify-center'
                : 'grid grid-cols-1 gap-10 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
        )}>
            {videoStatus === 200 ? (
                videos && videos.length > 0 ? (
                    videos.map((video) => (
                        <VideoCard
                            key={video.id}
                            {...video}
                            workspaceId={workspaceId}
                        />
                    ))
                ) : (
                    <div className='text-center text-neutral-400'>
                        <p className='text-lg mb-2'>No videos yet</p>
                        <p className='text-sm'>Click "Add Video" to create your first video</p>
                    </div>
                )
            ) : (
                <div className='text-center text-neutral-400'>
                    <p className='text-lg mb-2'>Unable to load videos</p>
                    <p className='text-sm'>Please try refreshing the page</p>
                </div>
            )}
        </section>
    </div>
  )
}

export default Videos