import { useState } from "react"
import axios from "axios"

export const useSubscription = () => {
    const [isProcessing, setIsProcessing] = useState(false)
    interface PaymentResponse {
        status: number;
        session_url: string;
        customer_id?: string;
    }

    const onSubscribe = async () => {
        setIsProcessing(true)
        try {
            const response = await axios.get<PaymentResponse>('/api/payment')
            console.log('Payment response:', response.data) // Debug log

            if(response.data.status === 200 && response.data.session_url){
                window.location.href = response.data.session_url
                return
            } else {
                console.error('Payment failed:', response.data)
            }
        } catch (error) {
            console.error('Subscription error:', error)
        } finally {
            setIsProcessing(false)
        }
    }
    return {isProcessing, onSubscribe}
}