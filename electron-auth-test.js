// Electron.js Authentication Test Script
// This demonstrates how to authenticate with your Next.js API from Electron

const fetch = require('node-fetch'); // You'll need to install: npm install node-fetch

const API_BASE_URL = 'http://localhost:3000';

// Test GET method - Create user with Electron detection
async function testGetAuth() {
    console.log('🧪 Testing GET /api/auth/[id] with Electron headers...');
    
    try {
        const response = await fetch(`${API_BASE_URL}/api/auth/user_electron_test_123`, {
            method: 'GET',
            headers: {
                'x-electron-app': 'true',
                'user-agent': 'Electron/1.0 MyApp/1.0.0',
                'Content-Type': 'application/json'
            }
        });

        const data = await response.json();
        console.log('✅ GET Response:', data);
        return data;
    } catch (error) {
        console.error('❌ GET Error:', error);
    }
}

// Test POST method - Create user with custom data
async function testPostAuth() {
    console.log('🧪 Testing POST /api/auth/[id] with custom user data...');
    
    const userData = {
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        workspaceName: 'John\'s Electron Workspace'
    };

    try {
        const response = await fetch(`${API_BASE_URL}/api/auth/user_electron_post_456`, {
            method: 'POST',
            headers: {
                'x-electron-app': 'true',
                'user-agent': 'Electron/1.0 MyApp/1.0.0',
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(userData)
        });

        const data = await response.json();
        console.log('✅ POST Response:', data);
        return data;
    } catch (error) {
        console.error('❌ POST Error:', error);
    }
}

// Test CORS preflight
async function testCORS() {
    console.log('🧪 Testing CORS preflight...');
    
    try {
        const response = await fetch(`${API_BASE_URL}/api/auth/user_test`, {
            method: 'OPTIONS',
            headers: {
                'Origin': 'http://localhost:5173',
                'Access-Control-Request-Method': 'POST',
                'Access-Control-Request-Headers': 'Content-Type, Authorization'
            }
        });

        console.log('✅ CORS Status:', response.status);
        console.log('✅ CORS Headers:', Object.fromEntries(response.headers.entries()));
    } catch (error) {
        console.error('❌ CORS Error:', error);
    }
}

// Main test function
async function runTests() {
    console.log('🚀 Starting Electron.js Auth API Tests...\n');
    
    // Test CORS first
    await testCORS();
    console.log('\n' + '='.repeat(50) + '\n');
    
    // Test GET method
    await testGetAuth();
    console.log('\n' + '='.repeat(50) + '\n');
    
    // Test POST method
    await testPostAuth();
    console.log('\n' + '='.repeat(50) + '\n');
    
    console.log('✅ All tests completed!');
}

// Run tests if this file is executed directly
if (require.main === module) {
    runTests();
}

module.exports = {
    testGetAuth,
    testPostAuth,
    testCORS,
    runTests
};
