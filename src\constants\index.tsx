import { ReactNode } from "react";
import { SidebarBillingIcon, SidebarHomeIcon, SidebarLibraryIcon, SidebarNotificationIcon, SidebarSettingsIcon } from "@/components/icons/SidebarIcons";

export const MENU_ITEMS = ( workspaceId: string): {title:string; href: string; icon: ReactNode}[] => [
    {title: 'Home', href:`/dashboard/${workspaceId}/home`, 
    icon:<SidebarHomeIcon/>},

    {title: 'My Library', href:`/dashboard/${workspaceId}`, 
    icon:<SidebarLibraryIcon/>},

    {title: 'Notification', href:`/dashboard/${workspaceId}/notification`, 
    icon:<SidebarNotificationIcon/>},

    {title: 'Billing', href:`/dashboard/${workspaceId}/billing`, 
    icon:<SidebarBillingIcon/>},

    {title: 'Settings', href:`/dashboard/${workspaceId}/settings`, 
    icon:<SidebarSettingsIcon/>},

    // {title: 'Home', href:`/dashboard/${workspaceId}/home`, 
    // icon:<HomeIcon/>},
    // {title: 'Home', href:`/dashboard/${workspaceId}/home`, 
    // icon:<HomeIcon/>},
]