import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Input } from '@/components/ui/input'
import { Skeleton } from '@/components/ui/skeleton'
import { useMutationData } from '@/hooks/useMutationData'
import { useSearch } from '@/hooks/useSearch'
import React from 'react'
import Loader from '../loader'
import { User } from 'lucide-react'
import { inviteMembers } from '@/app/actions/user'
import { Button } from '@/components/ui/button'

type Props = {
    workspaceId: string
}

const Search = ({workspaceId}: Props) => {
    const {query, onSearchQuery, onUser, isFetching} = useSearch('get-users', 'USERS')

    const {mutate, isPending} = useMutationData(
      ['invite-member'],
      (data:{recieverId: string; email: string}) => inviteMembers(
        data.email,
        workspaceId,
        data.recieverId
      ),
      'get-users'
    )
    
  return (
    <div className='flex flex-col gap-y-5'>
      <Input 
      onChange={onSearchQuery}
      value={query}
      className='bg-transparent border-2 outline-none'
      placeholder='Search for users..'
      type='text'
      />

      {isFetching ? (
        <div>
          <Skeleton className='w-full h-8 rounded-xl'/>
        </div>
      ): !onUser ? (
        <div>
          <p className='text-center text-sm text-[#a4a4a4]'>No users Found</p>
        </div>
      ): (
        <div>
          {onUser.map((user) => (
             <div key={user.id}
        className='flex gap-3 items-center border-2 w-full p-3 rounded-xl'
        >
          <Avatar>
            <AvatarImage src={user.image as string} />
            <AvatarFallback>
              <User/>
            </AvatarFallback>
          </Avatar>
          <div className='flex flex-col items-start capitalize'>
            <h3 className='flex flex-col items-start'>{user.firstName} {user.lastName}</h3>
            {/* <p className='lowercase text-xs text-gray-400'>
      
            </p> */}
            <p className='lowercase text-xs bg-white px-2 rounded-lg text-[#1e1e1e]'>
              {user.subscription?.plan}
            </p>
          </div>
          <div className='flex-1 flex justify-end items-center'>
            <Button
              onClick={() => mutate({recieverId: user.id, email: user.email as string})}
              className='w-5/12 font-bold'
              variant={'default'}
              disabled={isPending}
            >
              <Loader state={isPending} color='#000'>Invite</Loader>
            </Button>
          </div>

        </div>
          ))}
        </div>
      )}
      
    </div>
  )
}

export default Search