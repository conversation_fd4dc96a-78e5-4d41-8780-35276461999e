'use client'
import { getWorkspaces } from '@/app/actions/workspace'
import { useQueryData } from '@/hooks/useQueryData'
import React from 'react'
import Modal from '../modal'
import { Button } from '@/components/ui/button'
import { SidebarFolderPlusDuotoneIcon } from '@/components/icons/SidebarIcons'
import WorkspaceForm from '@/components/form/workspace-form'

type Props = {}

const CreateWorkspace = (props: Props) => {

  const { data } = useQueryData(['user-workspaces'], getWorkspaces)

  const { status, data: workspaces } = data as {
    status: number
    data: {
      id: string
      name: string
      type: 'PUBLIC' | 'PRIVATE'
      User: {
        subscription: {
          plan: 'FREE' | 'PRO'
        } | null
      } | null
    }[]
  }

  // If no data or error, don't show button
  if (status !== 200 || !workspaces) {
    return <></>
  }

  // Get the user's subscription plan from the first workspace
  const userPlan = workspaces[0]?.User?.subscription?.plan || 'FREE'
  const workspaceCount = workspaces.length

  // Hide button if FREE user already has 2 workspaces
  if (userPlan === 'FREE' && workspaceCount >= 2) {
    return <></>
  }

  // Show button for all users within their limits
  return (
    <Modal
      title='Create a Workspace'
      description='Create a new workspace to organize your videos and collaborate with others.'
      trigger={
        <Button className='bg-[#1D1D1D] text-[#707070] flex items-center gap-2 py-6 px-4 rounded-2xl'>
          <SidebarFolderPlusDuotoneIcon />
          Create a workspace
        </Button>
      }
    >
      <WorkspaceForm/>
    </Modal>
  )
}

export default CreateWorkspace