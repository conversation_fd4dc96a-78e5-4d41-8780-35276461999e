import { NextRequest, NextResponse } from "next/server";
import { client } from "@/lib/prisma"
import { clerkClient } from "@clerk/nextjs/server";

export async function GET(
    req: NextRequest,
    { params }: { params: Promise<{ id: string }> }
) {
    console.log('🔥 Auth API hit - GET /api/auth/[id]')
    try {
        const { id } = await params
        console.log('📋 Received ID:', id)

        // Check if this is an Electron.js request
        const userAgent = req.headers.get('user-agent') || ''
        const isElectronRequest = userAgent.includes('Electron') || req.headers.get('x-electron-app') === 'true'
        console.log('🖥️ Is Electron request:', isElectronRequest)

        // First, try to find existing user profile
        console.log('🔍 Searching for existing user in database...')
        const userProfile = await client.user.findUnique({
            where: {
                clerkId: id,
            },
            include: {
                subscription: {
                    select: {
                        plan: true,
                    }
                },
                Workspace: true
            },
        })

        if (userProfile) {
            console.log('✅ Found existing user:', userProfile.email)
            return NextResponse.json({ status: 200, user: userProfile })
        }

        console.log('❌ User not found in database, creating new user...')

        // For Electron.js requests, we might not have access to Clerk API
        // So we'll create a basic user profile if the ID looks valid
        if (isElectronRequest && id.startsWith('user_')) {
            console.log('🖥️ Creating user for Electron.js app...')
            const createUser = await client.user.create({
                data: {
                    clerkId: id,
                    email: `${id}@electron.app`, // Temporary email for Electron users
                    firstName: 'Electron',
                    lastName: 'User',
                    image: '',
                    studio: {
                        create: {}
                    },
                    Workspace: {
                        create: {
                            name: `Electron User's Workspace`,
                            type: "PRIVATE"
                        }
                    },
                    subscription: {
                        create: {}
                    }
                },
                include: {
                    subscription: {
                        select: {
                            plan: true,
                        }
                    },
                    Workspace: true
                }
            })

            if (createUser) {
                console.log('✅ New Electron user created successfully:', createUser.email)
                return NextResponse.json({ status: 201, user: createUser })
            }
        }

        // If user doesn't exist in database, get from Clerk and create
        console.log('🔗 Getting user from Clerk...')
        const clerk = await clerkClient();
        const clerkUserInstance = await clerk.users.getUser(id)
        console.log('👤 Clerk user found:', clerkUserInstance.emailAddresses[0]?.emailAddress)

            // Create new user in database
            console.log('💾 Creating new user in database...')
            const createUser = await client.user.create({
                data: {
                    clerkId: id,
                    email: clerkUserInstance.emailAddresses[0]?.emailAddress || '',
                    firstName: clerkUserInstance.firstName ?? '',
                    lastName: clerkUserInstance.lastName ?? '',
                    image: clerkUserInstance.imageUrl,
                    studio: {
                        create: {}
                    },
                    Workspace: {
                        create: {
                            name: `${clerkUserInstance.firstName ?? ''}'s Workspace`,
                            type: "PRIVATE"
                        }
                    },
                    subscription: {
                        create: {
                           
                        }
                    }
                },
                include: {
                    subscription: {
                        select: {
                            plan: true,
                        }
                    }
                }
            })

        if(createUser){
            console.log('✅ New user created successfully:', createUser.email)
            return NextResponse.json({ status: 201, user: createUser })
        }

        console.log('❌ Failed to create user')
        return NextResponse.json({ status: 400, message: 'User not found' })

    } catch (error) {
        console.error('💥 Auth API error:', error)
        return NextResponse.json({ status: 500, message: 'Internal server error' })
    }
}

// POST method for Electron.js to create/update user with provided data
export async function POST(
    req: NextRequest,
    { params }: { params: Promise<{ id: string }> }
) {
    console.log('🔥 Auth API POST hit - /api/auth/[id]')
    try {
        const { id } = await params
        const body = await req.json()
        console.log('📋 Received ID:', id)
        console.log('📦 Received data:', body)

        // Check if user already exists
        const existingUser = await client.user.findUnique({
            where: { clerkId: id },
            include: {
                subscription: { select: { plan: true } },
                Workspace: true
            }
        })

        if (existingUser) {
            console.log('✅ User already exists, returning existing user')
            return NextResponse.json({ status: 200, user: existingUser })
        }

        // Create new user with provided data
        const createUser = await client.user.create({
            data: {
                clerkId: id,
                email: body.email || `${id}@electron.app`,
                firstName: body.firstName || 'Electron',
                lastName: body.lastName || 'User',
                image: body.image || '',
                studio: {
                    create: {}
                },
                Workspace: {
                    create: {
                        name: body.workspaceName || `${body.firstName || 'Electron'}'s Workspace`,
                        type: "PRIVATE"
                    }
                },
                subscription: {
                    create: {}
                }
            },
            include: {
                subscription: { select: { plan: true } },
                Workspace: true
            }
        })

        if (createUser) {
            console.log('✅ New user created via POST:', createUser.email)
            return NextResponse.json({ status: 201, user: createUser })
        }

        return NextResponse.json({ status: 400, message: 'Failed to create user' })

    } catch (error) {
        console.error('💥 Auth API POST error:', error)
        return NextResponse.json({ status: 500, message: 'Internal server error' })
    }
}