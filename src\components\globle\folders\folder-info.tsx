'use client'
import { getFolderInfo } from '@/app/actions/workspace'
import { useQueryData } from '@/hooks/useQueryData'
import React from 'react'
import { FolderProps } from '@/types/index.type'

type Props = {
    folderId: string
}

const FolderInfo = ({folderId}: Props) => {

    const {data} = useQueryData(["folder-info"], () =>
         getFolderInfo(folderId))

    const {status, data: folder} = data as FolderProps

    if (status !== 200 || !folder) {
        return (
            <div className='flex items-center'>
                <h2 className='text-[#BdBdBd] text-2xl '>
                    Folder not found
                </h2>
            </div>
        )
    }

  return (
    <div className='flex items-center'>
        <h2 className='text-[#BdBdBd] text-2xl '>
            {folder.name}
        </h2>
        <span className='text-sm text-neutral-500 ml-4'>
            {folder._count?.videos || 0} videos
        </span>
    </div>
  )
}

export default FolderInfo