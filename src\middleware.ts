// import { clerkMiddleware, createRouteMatcher } from '@clerk/nextjs/server';
// // import { protect } from './auth'; 

import { clerkMiddleware, createRouteMatcher } from "@clerk/nextjs/server"
import { NextRequest, NextResponse } from "next/server"

// // export default clerkMiddleware();

// const isProtectedRoutes = createRouteMatcher([
//     '/dashboard',
//     '/api/payment',
//     '/payment(.*)',
// ])

// export default clerkMiddleware(async (auth , req) => {
//     if(isProtectedRoutes(req)){
//         await auth.protect()
//     }
// })


const allowedOrigins = ['http://localhost:3000', 'https://localhost:5173']

const corsOptions = {
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
}

const isProtectedRoutes = createRouteMatcher([
  '/dashboard',
  '/payment(.*)',
])

export default clerkMiddleware(async (auth, req: NextRequest) => {
  const origin = req.headers.get('origin') ?? ''
  const isAllowedOrigin = allowedOrigins.includes(origin)

  if(req.method === 'OPTIONS'){
    const prelightHeaders = {
      ...(isAllowedOrigin && {'Access-Control-Allow-Origin': origin}),
      ...corsOptions,
    }
    return NextResponse.json({}, {headers: prelightHeaders})
  }
  
  if(isProtectedRoutes(req)){
    await auth.protect()
  }
  
  const response = NextResponse.next()
  if(isAllowedOrigin){
    response.headers.set('Access-Control-Allow-Origin', origin)
  }
  Object.entries(corsOptions).forEach(([key, value]) => {
    response.headers.set(key, value)
  })
  return response
})


export const config = {
  matcher: [
    // Skip Next.js internals and all static files, unless found in search params
    '/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',
    // Always run for API routes
    '/(api|trpc)(.*)',
  ],
};