import { SignIn } from '@clerk/nextjs'
import { currentUser } from '@clerk/nextjs/server'
import { redirect } from 'next/navigation'
import React from 'react'

type Props = {}

const SignInPage = async (props: Props) => {
  const user = await currentUser()

  // If user is already signed in, redirect to dashboard
  if (user) {
    redirect('/dashboard')
  }

  return (
    <div className="flex items-center justify-center min-h-screen">
      <SignIn
        afterSignInUrl="/auth/callback"
        afterSignUpUrl="/auth/callback"
        signUpUrl="/auth/sign-up"
      />
    </div>
  )
}

export default SignInPage