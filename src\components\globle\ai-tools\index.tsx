import { Button } from '@/components/ui/button'
import { TabsContent } from '@/components/ui/tabs'
import React from 'react'
import Loader from '../loader'
import { SidebarFolderIcon, SidebarStarIcon, SidebarVideoRecorderIcon } from '@/components/icons/SidebarIcons'
import { Bot, Download, Pencil } from 'lucide-react'

type Props = {
    plan: 'FREE' | 'PRO'
    trial: boolean
    videoId: string
}

const AiTools = ({plan, trial, videoId}: Props) => {
    //wip: set the ai hook

  return (
    <TabsContent value='AI Tools'>
        {/* Header Section */}
        <div className='space-y-4'>
            <div className='flex items-start justify-between'>
                <div className='flex-1'>
                    <h2 className='text-2xl font-bold text-white mb-2'>AI Tools</h2>
                    <p className='text-gray-400 text-sm leading-relaxed'>
                        Taking your video to the next step with the power of AI!
                    </p>
                </div>
            </div>

            {/* Action Buttons */}
            <div className='flex flex-col sm:flex-row gap-3'>
                {plan === 'FREE' && !trial ? (
                    <>
                        <Button className='bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors'>
                            <Loader state={false} color='#fff'>
                                Try Now
                            </Loader>
                        </Button>
                        <Button variant='outline' className='border-gray-600 text-gray-300 hover:bg-gray-800 px-4 py-2 rounded-lg text-sm font-medium transition-colors'>
                            <Loader state={false} color='#9CA3AF'>
                                Upgrade to Pro
                            </Loader>
                        </Button>
                    </>
                ) : (
                    <Button className='bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-6 py-2 rounded-lg text-sm font-medium transition-all shadow-lg'>
                        <Loader state={false} color='#fff'>
                            Generate Now
                        </Loader>
                    </Button>
                )}
            </div>
        </div>
        {/* AI Features Section */}
        <div className='bg-gradient-to-br from-purple-900/20 to-pink-900/20 border border-purple-500/30 rounded-xl p-5 space-y-4'>
            <div className='flex items-center gap-3 mb-4'>
                <h3 className='text-xl font-bold text-purple-400'>Loom AI</h3>
                <SidebarStarIcon size={20} color='#a855f7' />
            </div>

            {/* AI Features List */}
            <div className='space-y-4'>
                {/* Video Summary */}
                <div className='flex gap-3 items-start p-3 rounded-lg bg-black/20 hover:bg-black/30 transition-colors'>
                    <div className='p-2 rounded-full bg-purple-600/20 border border-purple-500/30'>
                        <Pencil size={18} color='#a855f7'/>
                    </div>
                    <div className='flex-1'>
                        <h4 className='text-white font-medium mb-1'>Video Summary</h4>
                        <p className='text-gray-400 text-sm'>
                            Generate a comprehensive description for your video using AI.
                        </p>
                    </div>
                </div>

                {/* Video Transcript */}
                <div className='flex gap-3 items-start p-3 rounded-lg bg-black/20 hover:bg-black/30 transition-colors'>
                    <div className='p-2 rounded-full bg-purple-600/20 border border-purple-500/30'>
                        <SidebarFolderIcon size={18} color='#a855f7'/>
                    </div>
                    <div className='flex-1'>
                        <h4 className='text-white font-medium mb-1'>Smart Transcript</h4>
                        <p className='text-gray-400 text-sm'>
                            Create and read video transcripts with AI-powered accuracy.
                        </p>
                    </div>
                </div>

                {/* AI Agent */}
                <div className='flex gap-3 items-start p-3 rounded-lg bg-black/20 hover:bg-black/30 transition-colors'>
                    <div className='p-2 rounded-full bg-purple-600/20 border border-purple-500/30'>
                        <Bot size={18} color='#a855f7'/>
                    </div>
                    <div className='flex-1'>
                        <h4 className='text-white font-medium mb-1'>AI Agent</h4>
                        <p className='text-gray-400 text-sm'>
                            Viewers can ask questions about your video and our AI agent will respond.
                        </p>
                    </div>
                </div>
            </div>
        </div>
            

    </TabsContent>
  )
}

export default AiTools