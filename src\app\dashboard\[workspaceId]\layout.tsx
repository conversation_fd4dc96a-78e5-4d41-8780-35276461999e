import { getNotifications, onAuthenticateUser } from '@/app/actions/user'
import { getAllUserVideos, getWorkspaceFolders, getWorkspaces, verifyAccessToWorkspace } from '@/app/actions/workspace'
import { redirect } from 'next/navigation'
import React from 'react'
import {
  QueryClient,
  HydrationBoundary,
  dehydrate,
} from '@tanstack/react-query'
import Sidebar from '@/components/globle/sidebar'
import GlobalHeader from '@/components/globle/global-header'

type Props = {
    params: Promise<{workspaceId: string}>
    children: React.ReactNode
}

const Layout = async({params , children}: Props) => {
    const {workspaceId} = await params
    const auth = await onAuthenticateUser()
    if(!auth.user?.Workspace) redirect('/auth/sign-in')
    if(!auth.user.Workspace.length) redirect('/auth/sign-in')
    const hasAccess = await verifyAccessToWorkspace(workspaceId)

    if(hasAccess.status !== 200){
        redirect(`/dashboard/${auth.user?.Workspace[0].id}`)
    }
    if(!hasAccess.data?.workspace) return null

    const query = new QueryClient()
    await query.prefetchQuery({
        queryKey: ['workspace-folders'],
        queryFn: () => getWorkspaceFolders(workspaceId),
    })
    await query.prefetchQuery({
        queryKey: ['user-videos'],
        queryFn: () => getAllUserVideos(workspaceId),
    })
    await query.prefetchQuery({
        queryKey: ['user-workspaces'],
        queryFn: () => getWorkspaces(),
    })
    await query.prefetchQuery({
        queryKey: ['user-notification'],
        queryFn: () => getNotifications(),
    })

  return (
    <HydrationBoundary state={dehydrate(query)}>
        <div className='flex w-screen h-screen'>
      <Sidebar activeWorkspaceId={workspaceId} />
      <div className='w-full pt-28 p-6 overflow-y-scroll overflow-x-hidden'>
        <GlobalHeader workspace={hasAccess.data.workspace}/>
        <div className='mt-4'>
            {children}
        </div>
      </div>
        </div>
    </HydrationBoundary>
  )
}

export default Layout