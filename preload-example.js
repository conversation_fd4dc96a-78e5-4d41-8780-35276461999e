// preload.js - Electron preload script example
// This exposes auth functions to the renderer process safely

const { contextBridge, ipc<PERSON>enderer } = require('electron');

// Expose auth API to renderer process
contextBridge.exposeInMainWorld('electronAuth', {
    // Login/authenticate user
    login: async (userData) => {
        return await ipcRenderer.invoke('auth:login', userData);
    },

    // Get current user
    getCurrentUser: async () => {
        return await ipcRenderer.invoke('auth:getCurrentUser');
    },

    // Check if authenticated
    isAuthenticated: async () => {
        return await ipcRenderer.invoke('auth:isAuthenticated');
    },

    // Get user workspace
    getWorkspace: async () => {
        return await ipcRenderer.invoke('auth:getWorkspace');
    },

    // Logout
    logout: async () => {
        return await ipcRenderer.invoke('auth:logout');
    }
});

// Example usage in renderer process (your HTML/JS):
/*
// In your renderer JavaScript:

// Login with custom data
async function loginUser() {
    const result = await window.electronAuth.login({
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        workspaceName: 'John\'s Workspace'
    });
    
    if (result.success) {
        console.log('Login successful:', result.user);
        showDashboard(result.user);
    } else {
        console.error('Login failed:', result.error);
        showError(result.error);
    }
}

// Check authentication status
async function checkAuth() {
    const isAuth = await window.electronAuth.isAuthenticated();
    const user = await window.electronAuth.getCurrentUser();
    
    if (isAuth && user) {
        showDashboard(user);
    } else {
        showLoginForm();
    }
}

// Get workspace info
async function loadWorkspace() {
    const workspace = await window.electronAuth.getWorkspace();
    if (workspace) {
        console.log('Current workspace:', workspace.name);
        // Load workspace data
    }
}

// Logout
async function logout() {
    const result = await window.electronAuth.logout();
    if (result.success) {
        showLoginForm();
    }
}

// Initialize app
document.addEventListener('DOMContentLoaded', () => {
    checkAuth();
});
*/
