import React from 'react'
import Loader from '../loader'
import CardMenu from './video-cardmenu'
import CopyLink from './link'
import Link from 'next/link'

type Props = {
    User: {
        firstName: string | null
        lastName: string | null
        image: string | null
    } | null
    id: string
    Folder: {
        id: string
        name: string
    } | null
    createdAt: Date
    title: string | null
    processing: boolean
    source: string
    workspaceId: string
}

const VideoCard = (props: Props) => {

  const dayAgo = Math.floor(
    (new Date().getTime() - props.createdAt.getTime())
     / (1000 * 60 * 60 * 24)
  )

  return (
    <Loader state={false}>
      <div className='group relative w-56 h-56 bg-[#171717] rounded-lg border border-[#252525] overflow-hidden hover:border-[#404040] transition-colors cursor-pointer'>

        {/* Actions - Top Right */}
        <div className='absolute top-2 right-2 z-50 flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity'>
          <CardMenu
            currentFolder={props.Folder?.id}
            currentFolderName={props.Folder?.name}
            currentWorkspace={props.workspaceId}
            videoId={props.id}
          />
          <CopyLink
            className='p-1 h-5 w-5 bg-[#252525] hover:bg-[#404040] rounded text-xs'
            videoId={props.id}
          />
        </div>

        <Link 
        href={`/dashboard/${props.workspaceId}/video/${props.id}`}
        // href={`/preview/${props.id}`} 
        className='block w-full h-full'>
          {/* Video Thumbnail */}
          <div className='relative w-full h-36 bg-[#0a0a0a]'>
            <video
              controls={false}
              preload='metadata'
              className='w-full h-full object-cover opacity-60'
            >
              <source src={`${process.env.NEXT_PUBLIC_CLOUD_FRONT_STREAM_URL}/api/video/${props.source}#t=1`} />
            </video>

            {/* Play Button Overlay */}
            <div className='absolute inset-0 flex items-center justify-center'>
              <div className='w-10 h-10 bg-black/50 rounded-full flex items-center justify-center'>
                <svg className='w-5 h-5 text-white ml-0.5' fill='currentColor' viewBox='0 0 24 24'>
                  <path d='M8 5v14l11-7z'/>
                </svg>
              </div>
            </div>
          </div>

          {/* Video Info */}
          <div className='p-4 h-20 flex flex-col justify-between'>
            <h3 className='text-[#BDBDBD] font-medium text-sm truncate mb-1'>
              {props.title || 'Untitled Video'}
            </h3>

            <div className='flex items-center gap-2 text-xs text-[#707070]'>
              {props.User?.image && (
                <img
                  src={props.User.image}
                  alt="User"
                  className='w-4 h-4 rounded-full'
                />
              )}
              <span className='truncate'>
                {props.User?.firstName} {props.User?.lastName}
              </span>
            </div>

            <div className='flex justify-between items-center text-xs text-[#9D9D9D]'>
              <span>{dayAgo === 0 ? 'Today' : `${dayAgo}d ago`}</span>
              {props.Folder?.name && (
                <span className='truncate max-w-[80px]'>{props.Folder.name}</span>
              )}
            </div>
          </div>
        </Link>
      </div>
    </Loader>
  )
}

export default VideoCard