

export type WorkspaceProps = {
    data: {
        id: string
        name: string
        type: 'PUBL<PERSON>' | 'PRIVATE'
        User: {
            subscription: {
                plan: 'FREE' | 'PRO'
            } | null
        } | null
        members: {
            WorkSpace: {
                id: string
                name: string
                type: 'PUBLIC' | 'PRIVATE'
            } 
        }[]
    }[]
}

export type NotificationProps = {
    status: number
    data: {
        _count:{
            notification: number
        }
    }
}

export type FolderProps = {
    status: number
    data: {
        name: string
        _count: {
            videos: number
        }
    }
}

export type VideosProps = {
    status: number
    data: {
        User: {
            firstName: string | null
            lastName: string | null
            image: string | null
        } | null
        id: string
        processing: boolean
        Folder: {
            id: string
            name: string
        } | null
        createdAt: Date
        title: string | null
        source: string
        workspaceId: string
    }[]
}

export type VideoProps = {
    status: number
    data: {
        id: string
        title: string | null
        description: string | null
        source: string
        createdAt: Date
        views: number
        summery: string | null
        User: {
            id: string
            firstName: string | null
            lastName: string | null
            image: string | null
            trial: boolean
            subscription: {
                plan: 'FREE' | 'PRO'
            } | null
        } | null
        folder: {
            id: string
            name: string
        } | null
        Workspace: {
            id: string
            name: string
        } | null
    } | null
    author: boolean
}

export type CommentRepliesProps = {
    id: string
    content: string
    createdAt: Date
    commentId: string | null
    userId: string | null
    videoId: string | null
    User: {
        id: string
        firstName: string | null
        lastName: string | null
        image: string | null
        createdAt: Date
        clerkId: string
        trial: boolean
        fistview: boolean
    } | null
}

export type VideoCommentProps = {
    status: number
    data: {
        User:{
            id: string
            email: string
            firstName: string | null
            lastName: string | null
            image: string | null
            createdAt: Date
            clerkId: string
            trial: boolean
            fistview: boolean
        } | null
        id: string
        content: string | null
        comment: string | null
        createdAt: Date
        commentId: string | null
        userId: string | null
        videoId: string | null
        reply: CommentRepliesProps[]
    }[]
}