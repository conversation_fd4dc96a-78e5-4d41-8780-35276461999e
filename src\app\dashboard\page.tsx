import React from "react"
import { onAuthenticateUser } from "../actions/user"
import { redirect } from "next/navigation"


type Props = {}

const DashboardPage = async (props: Props) => {
    // Authentication
    const auth = await onAuthenticateUser()

    if (auth.status === "200" || auth.status === "201") {
        // Check if user has workspaces
        if (auth.user?.Workspace && auth.user.Workspace.length > 0) {
            redirect(`/dashboard/${auth.user.Workspace[0].id}`)
        } else {
            // Handle case where user has no workspaces (shouldn't happen with current setup)
            redirect('/auth/sign-in')
        }
    }

    // If authentication failed, redirect to sign-in
    redirect('/auth/sign-in')
}

export default DashboardPage