import type { <PERSON>ada<PERSON> } from "next";
import { Manrope } from "next/font/google";
import { <PERSON><PERSON><PERSON><PERSON> } from "@clerk/nextjs";
import "./globals.css";
import { ThemeProvider } from "@/components/theme";
import ReactQueryProvider from "@/react-qurey";
import { ReduxProvider } from "@/redux/provider";
import { Toaster } from "sonner";

const manrope = Manrope({
  subsets: ["latin"],
})


export const metadata: Metadata = {
  title: "Lomm",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <ClerkProvider>

    <html lang="en">
      <body
        className={`${manrope.className} bg-[#171717]`}
        >
          <ThemeProvider
            attribute="class"
            defaultTheme="dark"
            disableTransitionOnChange
          >
            <ReduxProvider>
            <ReactQueryProvider>
        {children}
        <Toaster />
            </ReactQueryProvider>
            </ReduxProvider>

          </ThemeProvider>
      </body>
    </html>
        </ClerkProvider>
  );
}
