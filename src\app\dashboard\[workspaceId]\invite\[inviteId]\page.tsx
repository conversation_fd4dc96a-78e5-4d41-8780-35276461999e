import { acceptInvite } from '@/app/actions/user'
import { redirect } from 'next/navigation'
import React from 'react'

type Props = {
    params: Promise<{
        workspaceId: string
        inviteId: string
    }>
}

const Page = async({params}: Props) => {
    const {inviteId, workspaceId} = await params
    const invite = await acceptInvite(inviteId)

    if(invite.status === 404) return redirect('/auth/sign-in')

    if(invite.status === 401) {
        return (
        <div className='flex flex-col gap-y-2 container justify-center items-center h-screen '>
      <h2 className='text-6xl font-bold text-white'>Not Authorized</h2>
      <p className='text-gray-400'>You are not authorized to accept this invite</p>
            </div>
        )
    }

    if(invite.status === 200) return redirect('/auth/callback')

  return (
    <div className='flex flex-col gap-y-2 container justify-center items-center h-screen '>
      <h2 className='text-6xl font-bold text-white'>Processing Invite</h2>
      <p className='text-gray-400'>Please wait while we process your invitation...</p>
    </div>
  )
}

export default Page