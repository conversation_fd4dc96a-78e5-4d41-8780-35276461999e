import FormGenerator from '@/components/globle/form-generator'
import Loader from '@/components/globle/loader'
import { Button } from '@/components/ui/button'
import { useCreateWorkspace } from '@/hooks/useCreateWorkspace'
import React from 'react'

type Props = {}

const WorkspaceForm = (props: Props) => {
   const {errors, isPending, onFormSubmit, register} = useCreateWorkspace()
  return (
    <form onSubmit={onFormSubmit} className='flex flex-col gap-y-3'
    >
        <FormGenerator name='name'
        placeholder={'Workspace Name'}
        label='Workspace Name'
        inputType='input'
        register={register}
        errors={errors}
        type='text'
         />
         <Button className='text-sm w-full mt-2'
         type='submit'
         disabled={isPending}
         >
            <Loader state={isPending}>Create Workspace</Loader>
         </Button>
    </form>
  )
}

export default WorkspaceForm