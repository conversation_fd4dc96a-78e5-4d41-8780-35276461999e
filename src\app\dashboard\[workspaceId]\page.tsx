import CreateFolders from '@/components/globle/create-folders'
import CreateWorkspace from '@/components/globle/create-workspace'
import Folders from '@/components/globle/folders'
import Videos from '@/components/globle/video'

import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import React from 'react'

type Props = {
  params : Promise<{workspaceId: string}>
}

const Page = async ({params}: Props) => {
  const {workspaceId} = await params
  return (
    <div>
      <Tabs
       defaultValue='videos' 
       className='mt-6'>
    <div className='flex w-full justify-between items-center'>
      <TabsList className='bg-transparent gap-2 pl-0'>
        <TabsTrigger className='p-[13px] px-6 rounded-full data-[state=active]:bg-[#252525]' value='videos'> videos</TabsTrigger>
        <TabsTrigger className='p-[13px] px-6 rounded-full data-[state=active]:bg-[#252525]' value='Archives'> Archive</TabsTrigger>
        

      </TabsList>
      <div className='flex gap-3'>
        <CreateWorkspace/>
        <CreateFolders workspaceId={workspaceId} />
      </div>
    </div>
    <section>
      <TabsContent value='videos'>
       <Folders workspaceId={workspaceId}/>
       <Videos workspaceId={workspaceId} folderId="" videosKey="user-videos" />
      </TabsContent>
    </section>
      </Tabs>
    </div>
  )
}

export default Page