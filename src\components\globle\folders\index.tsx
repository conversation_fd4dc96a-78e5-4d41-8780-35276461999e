'use client'
import { SidebarFolderIcon } from '@/components/icons/SidebarIcons'
import { cn } from '@/lib/utils'
import { ArrowRight } from 'lucide-react'
import React from 'react'
import Folder from './folder'
import { useQueryData } from '@/hooks/useQueryData'
import { getWorkspaceFolders } from '@/app/actions/workspace'
import { useMutationDataState } from '@/hooks/useMutationData'
import {useDispatch} from 'react-redux'
import { FOLDERS } from '@/redux/slices/folders'

type Props = {
    workspaceId: string
}

export type FoldersProps = {
    status: number
    data: {
        _count: {
            videos: number
        }
    } & {
        id: string
        name: string
        createdAt: Date
        updatedAt: Date
        workspaceId: string | null
    }
} 

const Folders = ({workspaceId}: Props) => {
    const dispatch = useDispatch()
    // get all folders
    const {data , isFetched} = useQueryData(['workspace-folders'], () => getWorkspaceFolders(workspaceId))

    const {latestVariables} = useMutationDataState([
        'create-folder'])

    const { status, data: folders} = data as { status: number; data: FoldersProps['data'][] }

    if(isFetched && folders){
         dispatch(FOLDERS({folders: folders}))

    }


    //WTP:add redux stuff for folders


    //optimitic variable



  return (
    <div className='flex flex-col gap-4'
    suppressHydrationWarning
    >
        <div className='flex items-center justify-between'>
        <div className='flex items-center gap-4'>
            <SidebarFolderIcon size={22} color='#ffffff' />
            <h2 className='text-[#BDBDBD]'>Folders</h2>
        </div>
        <div className='flex items-center gap-2'>
            <p className='text-[#BDBDBD]'>See All</p>
            <ArrowRight color='#707070'/>
        </div>
        </div>
        <div className={cn(
            status !== 200
                ? "justify-center flex items-center"
                : "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4"
        )}>

        { status !== 200 ? (
            <p className='text-neutral-300'>No folders in workspace</p>
        ): (
            <>
            {latestVariables && latestVariables.status ==="pending" && (
                <Folder name={latestVariables.variables.name}
                id={latestVariables.variables.id}
                optimistic
                />
            )}
            {folders.map((folder) => (
                <Folder
                key={folder.id}
                name={folder.name}
                 id={folder.id}
                 count={folder._count.videos}
                 />
            ))}
            </>
        )}
        </div>
    </div>
  )
}

export default Folders