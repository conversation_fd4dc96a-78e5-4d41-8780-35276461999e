// Electron.js Integration Example
// This shows how to integrate the auth API into your Electron app

const { app, BrowserWindow, ipcMain } = require('electron');
const fetch = require('node-fetch'); // npm install node-fetch

const API_BASE_URL = 'http://localhost:3000';

class ElectronAuthManager {
    constructor() {
        this.currentUser = null;
        this.isAuthenticated = false;
    }

    // Generate a unique user ID for Electron users
    generateElectronUserId() {
        return `user_electron_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    // Authenticate or create user
    async authenticateUser(userData = {}) {
        const userId = userData.userId || this.generateElectronUserId();
        
        try {
            console.log('🔐 Authenticating Electron user:', userId);
            
            // Try GET first to see if user exists
            let response = await fetch(`${API_BASE_URL}/api/auth/${userId}`, {
                method: 'GET',
                headers: {
                    'x-electron-app': 'true',
                    'user-agent': `Electron/${process.versions.electron} ${app.getName()}/${app.getVersion()}`,
                    'Content-Type': 'application/json'
                }
            });

            let data = await response.json();

            // If user doesn't exist and we have custom data, create with POST
            if (data.status !== 200 && data.status !== 201 && Object.keys(userData).length > 1) {
                console.log('👤 Creating user with custom data...');
                
                response = await fetch(`${API_BASE_URL}/api/auth/${userId}`, {
                    method: 'POST',
                    headers: {
                        'x-electron-app': 'true',
                        'user-agent': `Electron/${process.versions.electron} ${app.getName()}/${app.getVersion()}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: userData.email || `${userId}@electron.app`,
                        firstName: userData.firstName || 'Electron',
                        lastName: userData.lastName || 'User',
                        workspaceName: userData.workspaceName || `${userData.firstName || 'Electron'}'s Workspace`
                    })
                });

                data = await response.json();
            }

            if (data.status === 200 || data.status === 201) {
                this.currentUser = data.user;
                this.isAuthenticated = true;
                console.log('✅ Authentication successful:', this.currentUser.email);
                return { success: true, user: this.currentUser };
            } else {
                console.error('❌ Authentication failed:', data);
                return { success: false, error: data.message };
            }

        } catch (error) {
            console.error('💥 Authentication error:', error);
            return { success: false, error: error.message };
        }
    }

    // Get current user info
    getCurrentUser() {
        return this.currentUser;
    }

    // Check if user is authenticated
    isUserAuthenticated() {
        return this.isAuthenticated;
    }

    // Get user's workspace
    getUserWorkspace() {
        return this.currentUser?.Workspace?.[0] || null;
    }

    // Logout (clear local data)
    logout() {
        this.currentUser = null;
        this.isAuthenticated = false;
        console.log('👋 User logged out');
    }
}

// Create auth manager instance
const authManager = new ElectronAuthManager();

// IPC handlers for renderer process
ipcMain.handle('auth:login', async (event, userData) => {
    return await authManager.authenticateUser(userData);
});

ipcMain.handle('auth:getCurrentUser', () => {
    return authManager.getCurrentUser();
});

ipcMain.handle('auth:isAuthenticated', () => {
    return authManager.isUserAuthenticated();
});

ipcMain.handle('auth:getWorkspace', () => {
    return authManager.getUserWorkspace();
});

ipcMain.handle('auth:logout', () => {
    authManager.logout();
    return { success: true };
});

// Example usage in main process
async function createWindow() {
    const mainWindow = new BrowserWindow({
        width: 1200,
        height: 800,
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            preload: path.join(__dirname, 'preload.js') // You'll need to create this
        }
    });

    // Auto-authenticate on app start
    const authResult = await authManager.authenticateUser({
        email: '<EMAIL>',
        firstName: 'Electron',
        lastName: 'User',
        workspaceName: 'My Electron Workspace'
    });

    if (authResult.success) {
        console.log('🎉 Auto-authentication successful!');
        // Load your app's main page
        mainWindow.loadFile('index.html');
    } else {
        console.error('❌ Auto-authentication failed:', authResult.error);
        // Load login page or show error
        mainWindow.loadFile('login.html');
    }
}

app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
        createWindow();
    }
});

module.exports = { authManager };
