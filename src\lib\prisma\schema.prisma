generator client {
  provider = "prisma-client-js"
  output   = "../src/lib/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id           String         @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  clerkId      String         @unique
  email        String         @unique
  firstName    String
  lastName     String
  image        String?
  createdAt    DateTime       @default(now())
  updatedAt    DateTime       @updatedAt
  trial        Boolean        @default(false)
  reciver      Invite[]       @relation("reciver")
  sender       Invite[]       @relation("sender")
  studio       Media?
  members      Member[]
  notification Notification[]
  subscription Subscription?
  videos       Video[]
  Workspace    Workspace[]
}

model Subscription {
  id         String            @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId     String?           @unique @db.Uuid
  plan       SUBSCRIPTION_PLAN @default(FREE)
  createdAt  DateTime          @default(now())
  updatedAt  DateTime          @updatedAt
  customerId String?           @unique
  User       User?             @relation(fields: [userId], references: [id])
}

model Media {
  id     String  @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  screen String?
  mic    String?
  camera String?
  preset PRESET  @default(SD)
  userId String? @unique @db.Uuid
  User   User?   @relation(fields: [userId], references: [id])
}

model Workspace {
  id        String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name      String
  userId    String?  @db.Uuid
  type      Type
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  Folders   Folder[]
  invites   Invite[]
  members   Member[]
  videos    Video[]
  User      User?    @relation(fields: [userId], references: [id])
}

model Folder {
  id          String     @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name        String     @default("Untitled Folder")
  workspaceId String?    @db.Uuid
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  Workspace   Workspace? @relation(fields: [workspaceId], references: [id])
  videos      Video?
}

model Video {
  id          String     @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  title       String?    @default("Untitled Video")
  name        String
  description String?    @default("No description")
  source      String     @unique
  folderId    String?    @unique @db.Uuid
  userId      String?    @db.Uuid
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  workspaceId String?    @db.Uuid
  views       Int        @default(0)
  summery     String?
  folder      Folder?    @relation(fields: [folderId], references: [id], onDelete: Cascade)
  User        User?      @relation(fields: [userId], references: [id], onDelete: Cascade)
  Workspace   Workspace? @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
}

model Member {
  id          String     @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId      String?    @db.Uuid
  workspaceId String?    @db.Uuid
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  member      Boolean    @default(true)
  User        User?      @relation(fields: [userId], references: [id])
  Workspace   Workspace? @relation(fields: [workspaceId], references: [id])
}

model Notification {
  id      String  @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId  String? @db.Uuid
  content String
  User    User?   @relation(fields: [userId], references: [id])
}

model Invite {
  id          String     @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  senderId    String?    @db.Uuid
  reciverId   String?    @db.Uuid
  content     String
  workspaceId String?    @db.Uuid
  accepted    Boolean    @default(false)
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  reciver     User?      @relation("reciver", fields: [reciverId], references: [id])
  sender      User?      @relation("sender", fields: [senderId], references: [id])
  Workspace   Workspace? @relation(fields: [workspaceId], references: [id])
}

enum PRESET {
  SD
  HD
}

enum Type {
  PUBLIC
  PRIVATE
}

enum SUBSCRIPTION_PLAN {
  PRO
  FREE
}
