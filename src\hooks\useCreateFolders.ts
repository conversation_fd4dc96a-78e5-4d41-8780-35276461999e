import { createFolder } from "@/app/actions/workspace"
import { useMutationData } from "./useMutationData"

export const useCreateFolders = (workspaceId: string) => {
    const { mutate, isPending } = useMutationData(
        ["create-folder"],
        (data: {name: string}) => createFolder(workspaceId, data.name),
        'workspace-folders'
    )

    const onCreateNewFolder = () =>
        mutate({name: 'Untitled', id: 'optimistic--id'})

    return { onCreateNewFolder, isPending }
}