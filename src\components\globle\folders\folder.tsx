'use client'
import { cn } from '@/lib/utils'
import { usePathname, useRouter } from 'next/navigation'
import React, { useRef, useState, useEffect } from 'react'
import Loader from '../loader'
import { SidebarFolderIcon } from '@/components/icons/SidebarIcons'
import { useMutationData, useMutationDataState } from '@/hooks/useMutationData'
import { renamedFolders } from '@/app/actions/workspace'
import { Input } from '@/components/ui/input'

type Props = {
    name: string
    id:string
    optimistic?: boolean
    count?: number
}

const Folder = ({id,name,optimistic,count}: Props) => {
    const inputRef = useRef<HTMLInputElement | null>(null)
    const foldercardRef = useRef<HTMLDivElement | null>(null)
    const pathName = usePathname()
    const router = useRouter()
    const [onRename, setOnRename] = useState(false)

    const Rename = () => setOnRename(true)
    const Renamed = () => setOnRename(false)

    //add loading state
    //optimitic
    const {mutate, isPending} = useMutationData(
    ['rename-folders'],
     (data:{name: string}) => renamedFolders(id, data.name),
    'workspace-folders')

    const {latestVariables} = useMutationDataState(['rename-folders'])

    const handleFolderClick = () => {
        if(onRename) return
        router.push(`${pathName}/folder/${id}`)
    }

    const handleNameDoubleClick = (e: React.MouseEvent<HTMLParagraphElement>) => {
        //rename folder
        e.stopPropagation()
        Rename()
    }

    const updateFolderName = (e: Event) => {
        if(inputRef.current && foldercardRef.current){
            if(!inputRef.current.contains(e.target as Node || null)){
                if(inputRef.current.value && inputRef.current.value.trim() !== name){
                    mutate({name: inputRef.current.value.trim()})
                }
                Renamed()
            }
        }
    }

    const handleSubmitRename = () => {
        if(inputRef.current?.value && inputRef.current.value.trim() !== name){
            mutate({name: inputRef.current.value.trim()})
        }
        Renamed()
    }

    const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
        if(e.key === 'Enter'){
            e.preventDefault()
            handleSubmitRename()
        }
        if(e.key === 'Escape'){
            Renamed()
        }
    }

    useEffect(() => {
        if(onRename){
            const timer = setTimeout(() => {
                inputRef.current?.focus()
                inputRef.current?.select()
            }, 0)

            document.addEventListener('click', updateFolderName)

            return () => {
                clearTimeout(timer)
                document.removeEventListener('click', updateFolderName)
            }
        }
    }, [onRename])


  return (
    <div
    onClick={handleFolderClick}
    ref={foldercardRef}
     className={cn(optimistic && 'opacity-60' ,'flex hover:bg-neutral-800 cursor-pointer transition duration-150 items-center gap-2 justify-between w-full max-w-xs py-3 px-3 rounded-md border border-neutral-600')}>

        <Loader state={false}>
        <div className='flex flex-col gap-[1px]'>
            {onRename ? 
            (<Input
            onBlur={() => {
                handleSubmitRename()
            }}
            autoFocus
             placeholder={name}
             className='border-none text-base w-full outline-none text-neutral-300 bg-transparent p-0'
             ref={inputRef}
             onKeyDown={handleKeyPress}
             onClick={(e) => e.stopPropagation()}
            /> 
        ) : (
            <p 
            onClick={(e) => e.stopPropagation()}
            className='text-neutral-300'
            onDoubleClick={(e) => {
                handleNameDoubleClick(e)
            }}
            >
            {(latestVariables &&
              latestVariables.status === 'pending' &&
              latestVariables.variables.id === id) ?
              latestVariables.variables.name : name}
            </p>
             )}
            <span className='text-sm text-neutral-500'>{count || 0} videos</span>
        </div>
        </Loader>
        <SidebarFolderIcon size={22} color='#ffffff'/>
        </div>
  )
}

export default Folder