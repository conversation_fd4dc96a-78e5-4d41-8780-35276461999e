import { useMutationData } from "./useMutationData"
import { createWorkspace } from "@/app/actions/workspace"
import useZodForm from "./useZodForm"
import { workspaceSchema } from "@/components/form/workspace-form/schema"

export const useCreateWorkspace = () => {
    const {mutate, isPending} = useMutationData(
        ['create-workspace'],
        (data: {name: string}) => createWorkspace(data.name),
        'user-workspaces'
    )
    const {errors, onFormSubmit, register,} = useZodForm(workspaceSchema,mutate)
    return { isPending,onFormSubmit, register, errors}
}