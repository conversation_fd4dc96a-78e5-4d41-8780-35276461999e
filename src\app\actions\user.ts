"use server"
import {  currentUser } from "@clerk/nextjs/server"
import { prisma as client } from "@/lib/prisma"
import nodemailer from 'nodemailer'
import <PERSON><PERSON> from "stripe"

const stripe = new Stripe(process.env.STRIPE_CLIENT_SECRET as string)

export const sendEmail = async ( to: string,
    subject: string,
    text: string,
    html?: string,) => {
   const transporter = nodemailer.createTransport({
    host: "smtp.gmail.com",
    port: 465,
    secure: true,
    auth: {
      user: process.env.MAILER_EMAIL,
      pass: process.env.MAILER_PASSWORD,
    },
  });

  const mailOptions = {
    from: process.env.MAILER_EMAIL,
    to,
    subject,
    text,
    html,
  };

  return {transporter  , mailOptions}
}



export const onAuthenticateUser = async () => {
    try{
        const user = await currentUser()

        if(!user){
            return {status: "403"}
        }
        const userExist = await client.user.findUnique({
            where: {
                clerkId: user.id
            },
            include: {
                Workspace: {
                    where: {
                        User:{
                            clerkId: user.id
                        }
                    }
                }
            }
        })

        if(userExist){
            return {status: "200", user: userExist}
        }
        const newUser = await client.user.create({
            data:{
                clerkId: user.id,
                email: user.emailAddresses[0].emailAddress,
                firstName: user.firstName || "",
                lastName: user.lastName || "",
                image: user.imageUrl,
                studio: {
                    create: {}
                },
                subscription: {
                    create: {}
                },
                Workspace: {
                    create: {
                        name: `${user.firstName}'s Workspace`,
                        type: "PRIVATE",
                    }
                }
            },
            include: {
                Workspace: {
                    where: {
                        User: {
                            clerkId: user.id
                        }
                    }
                },
                subscription : {
                    select: {
                        plan: true
                    }
                }
            }
        })
        if(newUser) {
            return {status: "201", user: newUser}
        }

        return {status: "400"}
    }catch(error){
        console.error('onAuthenticateUser: Error occurred:', error)
        return {status: "500"}
    }
    
    
}

export const getNotifications = async () => {
    try {
        const user = await currentUser()
        if(!user) return {status: 404}
        const notifications = await client.notification.findMany({
            where: {
                userId: user.id,
            },
            select: {
                id: true,
                content: true,
                userId: true,
            }
        })
        if(notifications && notifications.length > 0) return {
            status: 200,
            data: notifications,
        }
        return {status: 404 , data: []}
    } catch (error) {
        return {status: 400 , data: []}
    }
}

export const searchUsers = async (query: string) => {
    try{
        const user = await currentUser()
        if(!user) return {status: 404, data: []}

        if (!query || query.trim().length === 0) {
            return { status: 400, data: [] }
        }

        const users = await client.user.findMany({
            where: {
                OR: [
                    {firstName: {contains: query, mode: 'insensitive'}},
                    {lastName: {contains: query, mode: 'insensitive'}},
                    {email: {contains: query, mode: 'insensitive'}},
                ],
                NOT: [{clerkId: user.id}]
            },
            select: {
                id: true,
                subscription: {
                    select: {
                        plan: true,
                    }
                },
                firstName: true,
                lastName: true,
                email: true,
                image: true,
            },
            take: 10
        })

        if(users && users.length > 0){
            return {status: 200, data: users}
        }
        return {status: 404 , data: []}
    }catch(error){
        return {status: 500, data: []}
    }
}


export const getPaymentInfo = async () => {
    try{
        const user = await currentUser()
        if(!user) return {status: 404}
        const payment = await client.user.findUnique({
            where: {
                clerkId: user.id,
            },
            select: {
                subscription: {
                    select: {
                        plan: true,
                    }
                }
            }
        })
        if(payment){
            return {status: 200, data: payment}
        }
        return {status: 404 , data: null}
        
    }catch(error){
        return {status: 500, data: null}
    }
}

export const getFirstView = async () => {
    try{
        const user = await currentUser()
        if(!user) return {status: 404}
        const userData = await client.user.findUnique({
            where: {
                clerkId: user.id,
            },
            select: {
                fistview: true,
            }
        })
        if(userData){
            return {status: 200, data: userData.fistview}
        }
        return {status: 404 , data: false}
    }catch(error){
        return {status: 500, data: false}
    }
}

export const updateFirstView = async (newValue: boolean) => {
    try{
        const user = await currentUser()
        if(!user) return {status: 404}
        const userData = await client.user.update({
            where: {
                clerkId: user.id,
            },
            data: {
                fistview: newValue,
            }
        })
        if(userData){
            return {status: 200, data: userData.fistview}
        }
        return {status: 404 , data: null}
    }catch(error){
        return {status: 500, data: null}
    }
}

export const createCommentAndReply = async (
    userId: string,
    videoId: string,
    comment: string,
    commentId?: string | undefined
 ) => {
    try{
        // If commentId is provided, create a reply
        if (commentId) {
            const reply = await client.comment.update({
                where: {
                    id: commentId,
                },
                data: {
                    reply: {
                        create: {
                            userId,
                            videoId,
                            content: comment,
                        }
                    }
                }
            })
            if(reply){
                return {status: 200, data: 'Reply posted'}
            }
        } else {
            // Create a new top-level comment
            const newComment = await client.comment.create({
                data: {
                    userId,
                    videoId,
                    content: comment,
                }
            })
            if(newComment){
                return {status: 200, data: 'New Comment posted'}
            }
        }
        return {status: 400, data: null}

    }catch(error){
        console.error('Create comment error:', error)
        return {status: 500, data: null}
    }
}

export const getUserProfile = async () => {
    try{
        const user = await currentUser()
        if(!user) return {status: 404}
        const profileIdAndImage = await client.user.findUnique({
            where: {
                clerkId: user.id,
            },
            select: {
                id: true,
                image: true,
            }
        })
        if(profileIdAndImage){
            return {status: 200, data: profileIdAndImage}
        }
    }catch(error){
        return {status: 400, data: null}
    }
}

export const getVideoComments = async (id: string) => {
    try{
        const comments = await client.comment.findMany({
            where: {
                videoId: id,
                commentId: null, // Only top-level comments
            },
            select: {
                id: true,
                content: true,
                comment: true,
                createdAt: true,
                commentId: true,
                userId: true,
                videoId: true,
                User: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        image: true,
                        email: true,
                        createdAt: true,
                        clerkId: true,
                        trial: true,
                        fistview: true
                    }
                },
                reply: {
                    include: {
                        User: true
                    }
                }
            }
        })

        return {status: 200, data: comments}
    }catch(error){
        return {status: 400, data: []}
    }
}

export const inviteMembers = async (
      email: string,
      workspaceId: string,
      recieverId: string
    ) => {
    try{
        const user = await currentUser()
        if(!user) return {status: 404, data: 'User not authenticated'}

        const senderInfo = await client.user.findUnique({
           where:{
            clerkId: user.id,
           },
           select: {
           id: true,
           firstName: true,
           lastName: true,
           }
        })

        if(!senderInfo) return {status: 404, data: 'Sender not found'}
        const workspace = await client.workspace.findUnique({
            where: {
                id: workspaceId,
            },
            select: {
                name: true,
            }
        })

        if(!workspace) return {status: 404, data: 'Workspace not found'}

        // Check if user is already invited
        const existingInvite = await client.invite.findFirst({
            where: {
                senderId: senderInfo.id,
                reciverId: recieverId,
                workspaceId: workspaceId,
            }
        })

        if(existingInvite) return {status: 400, data: 'User already invited'}
                const invitation = await client.invite.create({
                    data: {
                        senderId: senderInfo.id,
                        reciverId: recieverId,
                        content: `You have been invited to ${workspace.name} click accept to confirm`,
                        workspaceId: workspaceId
                    },
                    select: {
                        id: true,
                    }
                })
         await client.user.update({
            where: {
                id: recieverId,
            },
            data: {
                notification: {
                    create: {
                        content: `${senderInfo.firstName} ${senderInfo.lastName} has invited you to join ${workspace.name}`,
                    }
                }
            },
        })
        if(invitation){
            const {transporter, mailOptions} =
            await sendEmail(email,
                'You got an invitation',
                `You have been invited to join ${workspace.name}`,
                `<a href="${process.env.NEXT_PUBLIC_HOST_URL}/dashboard/${workspaceId}/invite/${invitation.id}" style="background-color: #000; padding: 5px 10px; border-radius: 10px;">Accept Invite</a>`
            )
            transporter.sendMail(mailOptions, (error, info) => {
                if (error) {
                    console.error('Email error:', error);
                }else{
                    console.log('Email sent:', info);
                }
            });
            return {status: 200, data: 'invite sent'}
        }
        return {status: 400, data: 'invite failed'}
    }catch(error){
        console.error('Invite error:', error)
        return {status: 400, data: 'Something went wrong'}
    }
}

export const acceptInvite = async (inviteId: string) => {
    try{
        const user = await currentUser()
        if(!user) return {status: 404, data: 'User not authenticated'}

        const invitation = await client.invite.findUnique({
            where: {
                id: inviteId,
            },
            select: {
                workspaceId: true,
                reciver: {
                    select: {
                       clerkId: true,
                    }
                }
            }
        })

        if(!invitation) return {status: 404, data: 'Invitation not found'}
        if(user.id !== invitation?.reciver?.clerkId) return {status: 401, data: 'Not authorized'}
        const acceptedInvite = client.invite.update({
            where: {
                id: inviteId,
            },
            data: {
                accepted: true,
            },
        })

        const updateMember = client.user.update({
            where:{
                clerkId: user.id,
            },
            data: {
                members: {
                    create: {
                        workspaceId: invitation.workspaceId,
                    }
                }
            }
        })

        const memberTransaction = await client.$transaction([
            acceptedInvite,
            updateMember,
        ])

        if(memberTransaction)
             return {status: 200,}
        return {status: 400, data: 'Invite not found'}
    }catch(error){
        console.error('Accept invite error:', error)
        return {status: 400, data: 'Something went wrong'}
    }
}
       

export const completeSubscription = async (session_id: string) => {
    try{
        const user = await currentUser()
        if(!user) return {status: 404, data: 'User not authenticated'}

        const session = await stripe.checkout.sessions.retrieve(session_id)
        if(session) {
            const customer = await client.user.update({
                where:{
                    clerkId: user.id,
                },
                data: {
                    subscription: {
                        update: {
                            data:{
                                customerId: session.customer as string,
                                plan: 'PRO',
                            }
                        }
                    }
                }
            })
            if(customer) {return {status: 200, data: customer}}
        }
        return {status: 400, data: 'Something went wrong'}
    }catch(error){
        console.error('Complete subscription error:', error)
        return {status: 400, data: 'Something went wrong'}
    }
}