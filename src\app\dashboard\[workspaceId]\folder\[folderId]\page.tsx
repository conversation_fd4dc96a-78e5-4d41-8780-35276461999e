import { getFolderVideos, getFolderInfo } from '@/app/actions/workspace'
import FolderInfo from '@/components/globle/folders/folder-info'
import Videos from '@/components/globle/video'
import { dehydrate, HydrationBoundary, QueryClient } from '@tanstack/react-query'
import React from 'react'

type Props = {
    params: Promise<{
        folderId:string
        workspaceId:string
    }>
}

const page = async ({params}: Props) => {
    const {folderId, workspaceId} = await params

    const query = new QueryClient()
    await query.prefetchQuery({
        queryKey: ['folder-videos'],
        queryFn: () => getFolderVideos(folderId),
    })

    await query.prefetchQuery({
        queryKey: ['folder-info'],
        queryFn: () => getFolderInfo(folderId),
    })

  return (
    <HydrationBoundary state={dehydrate(query)}>
        <FolderInfo folderId={folderId}/>
        <Videos workspaceId={workspaceId} 
        folderId={folderId} videosKey='folder-videos'/>
    </HydrationBoundary>
  )
}

export default page