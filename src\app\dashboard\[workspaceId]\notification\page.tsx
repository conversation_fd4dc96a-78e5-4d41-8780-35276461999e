'use client'
import { getNotifications } from '@/app/actions/user'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { useQueryData } from '@/hooks/useQueryData'
import { <PERSON><PERSON>, <PERSON>, BellOff } from 'lucide-react'
import React from 'react'

type NotificationData = {
    status: number
    data: {
        id: string
        content: string
        userId: string
    }[]
}

const NotificationPage = () => {
    const { data: notifications } = useQueryData(['user-notification'], getNotifications)

    // Handle loading state
    if (!notifications) {
        return (
            <div className='flex justify-center items-center h-full w-full'>
                <div className='text-center space-y-4'>
                    <Bell className='w-12 h-12 text-gray-500 mx-auto animate-pulse' />
                    <p className='text-gray-400'>Loading notifications...</p>
                </div>
            </div>
        )
    }

    const { data: notificationList, status } = notifications as NotificationData

    if (status !== 200) {
        return (
            <div className='flex justify-center items-center h-full w-full'>
                <div className='text-center space-y-4'>
                    <BellOff className='w-12 h-12 text-gray-500 mx-auto' />
                    <p className='text-gray-400'>No notifications available</p>
                </div>
            </div>
        )
    }

    if (!notificationList || notificationList.length === 0) {
        return (
            <div className='flex justify-center items-center h-full w-full'>
                <div className='text-center space-y-4'>
                    <Bell className='w-12 h-12 text-gray-500 mx-auto' />
                    <p className='text-gray-400'>No notifications yet</p>
                    <p className='text-sm text-gray-500'>You'll see notifications here when you have them</p>
                </div>
            </div>
        )
    }

    return (
        <div className='space-y-6'>
            {/* Header */}
            <div className='flex items-center gap-3'>
                <Bell className='w-6 h-6 text-purple-400' />
                <h1 className='text-2xl font-bold text-white'>Notifications</h1>
                <span className='bg-purple-600 text-white text-xs px-2 py-1 rounded-full'>
                    {notificationList.length}
                </span>
            </div>

            {/* Notifications List */}
            <div className='space-y-3'>
                {notificationList.map((notification) => (
                    <div
                        key={notification.id}
                        className='bg-[#1A1A1A] border border-[#2A2A2A] rounded-lg p-4 hover:bg-[#1F1F1F] transition-colors'
                    >
                        <div className='flex gap-4 items-start'>
                            <Avatar className='w-10 h-10'>
                                <AvatarFallback className='bg-gradient-to-r from-purple-600 to-pink-600 text-white'>
                                    <User className='w-5 h-5' />
                                </AvatarFallback>
                            </Avatar>
                            <div className='flex-1'>
                                <p className='text-white leading-relaxed'>{notification.content}</p>
                                <p className='text-xs text-gray-500 mt-2'>Just now</p>
                            </div>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    )
}

export default NotificationPage