import { searchUsers } from "@/app/actions/user"
import { useEffect, useState } from "react"
import { useQueryData, } from "./useQueryData"

export const useSearch = (key: string, type: 'USERS') => {
    const [query,setQuery] = useState('')
    const [debounce, setDebounce] = useState('')
    const [onUser , setOnUser] = useState<
    {id:string, 
    subscription: {
        plan: 'FREE' | 'PRO',
    } | null, 
    firstName: string | null, 
    lastName: string | null, 
    image: string | null, 
    email: string |null, 
}[] | undefined>(undefined)

const onSearchQuery = (e: React.ChangeEvent<HTMLInputElement>) => {
    setQuery(e.target.value)
}
useEffect(() => {
    const delayInputTimeoutId = setTimeout(() => {
        setDebounce(query)
    }, 1000)
    return () => clearTimeout(delayInputTimeoutId)
}, [query])

const {refetch, isFetching} = useQueryData(
    [key, debounce],
    async ({ queryKey}) => {
        const searchQuery = queryKey[1] as string

        // Always return an array to prevent undefined errors
        if(!searchQuery || searchQuery.trim().length === 0) {
            setOnUser(undefined)
            return []
        }

        if(type === "USERS"){
            try {
                const users = await searchUsers(searchQuery)
                if (users.status === 200 && users.data) {
                    setOnUser(users.data)
                    return users.data
                } else {
                    setOnUser([])
                    return []
                }
            } catch (error) {
                console.error('Search error:', error)
                setOnUser([])
                return []
            }
        }

        setOnUser(undefined)
        return []
    },
    !!debounce && debounce.trim().length > 0 // Only enable query when there's a valid search term
)

    useEffect(() => {
        if(!debounce || debounce.trim().length === 0) {
            setOnUser(undefined)
        }
        // The query will automatically refetch when enabled changes due to debounce
    }, [debounce])
    return {onSearchQuery, query, isFetching , onUser}

}