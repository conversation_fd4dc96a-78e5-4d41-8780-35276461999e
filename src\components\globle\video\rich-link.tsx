import { Button } from '@/components/ui/button'
import React from 'react'
import { toast } from 'sonner'

type Props = {
    title: string
    description: string
    source: string
    id: string
}

const RichLink = ({description, title, source, id  }: Props) => {
    const CopyRichText = () => {
        const orignalTitle = title 
        const thumbnail = `<a style="display flex; flex-direction: column;
        gap: 10px;" href="${process.env.NEXT_PUBLIC_HOST_URL}/preview/${id}">
        <h3 style="text-decoration: none; color: black; margin: 0">
        ${orignalTitle}
        </h3>
        <p style="text-decoration: none; color: black; margin: 0">
        ${description}
        </p>
        <video 
        width='320'
        style="display: block;"
        >
        <source type='video/webm' src="${process.env.NEXT_PUBLIC_CLOUD_FRONT_STREAM_URL}/${source}#1" />
        </video>
        </a>`

        const thumbnailBlob = new Blob([thumbnail], {type: 'text/html'})
        const BlobTitle = new Blob([orignalTitle], {type: 'text/plain'})
        // const url = URL.createObjectURL(thumbnailBlob)
        // navigator.clipboard.writeText(url)
        const data =[
            new ClipboardItem({
                'text/html': thumbnailBlob,
                'text/plain': BlobTitle
            }),
        ]
        navigator.clipboard.write(data).then(() => {
            return toast('Embedded Link copied' , {
                description: 'Successfully copied emmbeded link'
            })
        })
    }
  return (
    <Button onClick={CopyRichText} className='rounded-full'
    >
        Get Embadded Code
    </Button>
  ) 
}

export default RichLink