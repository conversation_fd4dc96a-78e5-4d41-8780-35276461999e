import React from 'react'
import Modal from '../modal'
import { Move } from 'lucide-react'
import ChangeVideoLocation from '@/components/form/change-video-loaction'

type Props = {
    videoId: string
    currentWorkspace?: string
    currentFolder?: string
    currentFolderName?: string 

}

const CardMenu = ({videoId, currentWorkspace, currentFolder, currentFolderName }: Props) => {
  return (
    <Modal 
    className='flex  items-center cursor-pointer gap-x-2'
    description='This action cannot be undone. this will permanently delete your video account and remove your data from our servers.'
    title='Move to new workspace/Folder'
     trigger={
        <Move size={20} fill='#4f4f4f' className='text-[#4f4f4f]'/>
    }
    >
        <ChangeVideoLocation
        videoId={videoId} 
        currentWorkspace={currentWorkspace ?? ''}
        currentFolder={currentFolder ?? ''} 
        currentFolderName={currentFolderName ?? ''} />


    </Modal>
  )
}

export default CardMenu