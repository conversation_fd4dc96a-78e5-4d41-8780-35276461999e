'use client'

import { getFirstView, updateFirstView } from '@/app/actions/user'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { cn } from '@/lib/utils'
import React, { useEffect, useState } from 'react'
import { toast } from 'sonner'


const SettingPage = () => {

    const [firstView, setFirstView] = useState<undefined | boolean>(undefined)
    const [theme, setTheme] = useState<string>()

    useEffect(() => {
        if(firstView !== undefined) return
        const fetchData = async () => {
            const response = await getFirstView()
            if (response.status === 200) setFirstView(response?.data)
        }
        fetchData()
    }, [firstView])

    const switchState = async(checked: boolean) => {
       const view = await updateFirstView(checked)
       if(view){
        setFirstView(checked)
        toast("Success", {
            description: "First view setting updated successfully",
        })
       }else{
        toast("Failed", {
            description: "Failed to update setting",
        })
       }
    }

  return (
    <div>
      {/*
        <div className='grid grid-cols-1 gap-10 lg:grid-cols-5'>
          <div className='lg:col-span-4 flex lg:flex-row flex-col items-center gap-5'>
            <div className={cn( 'rounded-2xl overflow-hidden cursor-pointer border-4 border-transparen',
            theme == 'system' && 'border-purple-800'

            )}
            onClick={() => setTheme('system')}
            >
              <SystemMode/>
            </div>
            <div className={cn( 'rounded-2xl overflow-hidden cursor-pointer border-4 border-transparen',
            theme == 'dark' && 'border-purple-800'
            )}
            onClick={() => setTheme('light')}>
            </div>
            <LightMode/>
          </div>
          <div className={cn(
            'rounded-2xl overflow-hidden cursor-pointer border-4 border-transparen',
            theme == 'light' && 'border-purple-800'
          )}
          onClick={() => setTheme('dark')}>
            <DarkMode/>
          </div>
        </div>
      */}

      <h2 className='text-2xl font-bold mt-4'>Video Sharing Setting</h2>
      <p className='text-muted-foreground'>
        Enabling this will send you notifications when your video is shared.
      </p>
      <Label className='flex items-center gap-x-3 text-md'>
        Enable First View
        <Switch
        onCheckedChange={switchState}
        disabled={firstView === undefined}
        checked={firstView}
        />
      </Label>
    </div>
  )
}

export default SettingPage