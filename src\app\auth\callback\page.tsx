import React from "react"
// import { onAuthenticateUser } from "@/actions/user"
import { redirect } from "next/navigation"
import { onAuthenticateUser } from "@/app/actions/user"



const DashboardPage = async () => {
    // Authentication
    const auth = await onAuthenticateUser()

    if (auth.status === "200" || auth.status === "201") {
        // Check if user has workspaces
        if (auth.user?.Workspace && auth.user.Workspace.length > 0) {
            redirect(`/dashboard/${auth.user.Workspace[0].id}`)
        } else {
            // If no workspace, redirect to dashboard root which will handle workspace creation
            redirect('/dashboard')
        }
    }

    // If authentication failed, redirect to sign-in
    redirect('/auth/sign-in')
}



export default DashboardPage