import CommentForm from '@/components/form/comment-form'
import { TabsContent } from '@/components/ui/tabs'
import React from 'react'
import CommentCard from '../comment-card/CommentCard'
import { useQueryData } from '@/hooks/useQueryData'
import { getVideoComments } from '@/app/actions/user'
import { VideoCommentProps } from '@/types/index.type'

type Props = {
    author: string
    videoId: string
}

const Activites = ({author , videoId}: Props) => {

  const {data, isPending} = useQueryData(['video-comments'], () =>
     getVideoComments(videoId))

  // Safely handle the data structure
  const response = data as VideoCommentProps | undefined
  const status = response?.status || 400
  const comments = response?.data || []

  return (
    <TabsContent value='Activity' className='p-5 rounded-xl flex flex-col gap-y-5'>
        <CommentForm  author={author} videoId={videoId} />

        {isPending ? (
          <div className='text-center text-gray-400 py-8'>
              <p>Loading comments...</p>
          </div>
        ) : status === 200 && comments && comments.length > 0 ? (
          comments.map((comment) => (
              <CommentCard
              comment={comment.comment || comment.content || ''}
              key={comment.id}
              author={{
                  image: comment.User?.image || '',
                  firstName: comment.User?.firstName || '',
                  lastName: comment.User?.lastName || ''
              }}
              reply={comment.reply || []}
              videoId={videoId}
              isReply={false}
              commentId={comment.id}
              />
          ))
        ) : (
          <div className='text-center text-gray-400 py-8'>
              <p>No comments yet. Be the first to comment!</p>
          </div>
        )}

    </TabsContent>
  )
}

export default Activites