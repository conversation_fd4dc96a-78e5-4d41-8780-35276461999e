import Loader from '@/components/globle/loader'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { Skeleton } from '@/components/ui/skeleton'
import { useMoveVideos } from '@/hooks/useFolder'
import React from 'react'

type Props = {
    currentWorkspace?: string
    currentFolder?: string
    currentFolderName?: string
    videoId: string
}

const ChangeVideoLocation = ({ videoId, currentWorkspace, currentFolder, currentFolderName }: Props) => {


    // const workspace = 

    const { register,
        errors,
        onFormSubmit,
        isPending,
        isFetching,
        isFolders,
        workspaces,
        folders } = useMoveVideos(videoId, currentWorkspace!)

        const folder = folders.find((f) => f.id === currentFolder)
        const workspace = workspaces.find((w) => w.id === currentWorkspace)

    return (
        <form className=' flex flex-col gap-y-5' 
        onSubmit={onFormSubmit}
        >
            <div className='bordre-[1px] rounded-xl p-5'>
                <h2 className='text-sm mb-5 text-[#a4a4a4]'>Current workspace</h2>
                {workspace &&<p> {workspace.name}</p>
}
               <h2 className='text-sm text-[#a4a4a4] mt-4'>Current Folder</h2>
               {folder ? <p>{folder.name}</p>: 'This video has no folder'}
            </div>
            <Separator orientation='horizontal' />
            <div className='flex flex-col gap-y-5 p-5 border-[1px] rounded-xl'>
                <h2 className='text-sm text-[#a4a4a4]'>To</h2>
                <Label className='flex flex-col gap-y-2'>
                    <p className='text-xs text-[#a4a4a4] font-medium'>Workspace</p>
                    <div className='relative'>
                        <select className='w-full appearance-none rounded-lg text-sm bg-[#1a1a1a] border border-[#333333] p-3 pr-10 text-white focus:border-[#7320DD] focus:outline-none transition-colors cursor-pointer' {...register("workspace_id")}>
                            {workspaces.map((space) => (
                                <option key={space.id} value={space.id}
                                className='text-[#a4a4a4]'
                                >
                                    {space.name}
                                </option>
                            ))}
                        </select>
                        {/* <div className='absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none'>
                            <svg className='w-4 h-4 text-[#a4a4a4]' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                                <path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M19 9l-7 7-7-7' />
                            </svg>
                        </div> */}
                    </div>
                </Label>
                {isFetching ? (<Skeleton className='w-full h-[40px] rounded-xl'/>): (
                    <Label className='flex flex-col gap-y-2'>
                    <p className='text-xs text-[#a4a4a4] font-medium'>Folder in this workspace</p>
                    {isFolders && isFolders.length > 0 ? (
                        <select {...register("folder_id")} className='w-full appearance-none rounded-lg text-sm bg-[#1a1a1a] border border-[#333333] p-3 pr-10 text-white focus:border-[#7320DD] focus:outline-none transition-colors cursor-pointer'>
                            <option value="" className='text-[#a4a4a4]'>No folder (workspace root)</option>
                            {isFolders.map((folder) => (
                                <option
                                    className='text-[#a4a4a4]'
                                    key={folder.id}
                                    value={folder.id}
                                >
                                    {folder.name}
                                </option>
                            ))}
                        </select>
                    ): (
                        <p className='text-[#a4a4a4]'>No folders in this workspace</p>
                    )}
                    </Label>
                )}
            </div>
            <Button type="submit" disabled={isPending}>
                <Loader color='#000' state={isPending}>
                    Transfer
                </Loader>
            </Button>
        </form>
    )
}

export default ChangeVideoLocation