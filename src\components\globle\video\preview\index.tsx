'use client'
import { getPreviewVideo, sendEamilForFirstView } from '@/app/actions/workspace'
import { useQueryData } from '@/hooks/useQueryData'
import { VideoProps } from '@/types/index.type'
import { useRouter } from 'next/navigation'
// import { useRouter } from 'next/router'
import React, { useEffect } from 'react'
import CopyLink from '../link'
import RichLink from '../rich-link'
import { truncateString } from '@/lib/utils'
import { Download } from 'lucide-react'
import TabMenu from '../../tabs'
import AiTools from '../../ai-tools'
import VideoActivity from '../../video-activity'
import { TabsContent } from '@/components/ui/tabs'
import Activites from '../../activites'
import VideoTranscript from '../../video-transcript'

type Props = {
    videoId: string
}

const VideoPreview = ({videoId}: Props) => {

    //wip

    const router = useRouter()

    const notifyfirstView = async () => await sendEamilForFirstView(videoId)

    const {data} = useQueryData(['preview-video'], () =>
         getPreviewVideo(videoId))

    const {data: video, status , author} = data as VideoProps
    if(status !== 200 || !video) {
        router.push('/')
        return null
    }

        const dayAgo = Math.floor(
            (new Date().getTime() - video.createdAt.getTime())
             / (1000 * 60 * 60 * 24)
          )


          useEffect(() => {
            if(video.views === 0){
                notifyfirstView()
            }
            return() => {
                notifyfirstView()
            }
          }, [])

  return (
    <div className='text-white'>
        {/* Main Container */}
        <div className='max-w-7xl mx-auto'>
            <div className='grid grid-cols-1 xl:grid-cols-4 gap-6'>

                {/* Main Video Content - Left Side */}
                <div className='xl:col-span-3 space-y-4'>

                    {/* Video Player Section */}
                    <div className='bg-[#1A1A1A] rounded-2xl p-4 border border-[#2A2A2A]'>
                        <video
                            preload='metadata'
                            className='w-full aspect-video rounded-xl bg-black shadow-2xl'
                            controls
                            poster='/video-placeholder.jpg'
                        >
                            <source src={`${process.env.NEXT_PUBLIC_CLOUD_FRONT_STREAM_URL}/${video.source}#1`} />
                            Your browser does not support the video tag.
                        </video>
                    </div>

                    {/* Video Info Section */}
                    <div className='bg-[#1A1A1A] rounded-2xl p-4 border border-[#2A2A2A] space-y-4'>
                        {/* Title and Actions */}
                        <div className='flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4'>
                            <div className='flex-1'>
                                <h1 className='text-2xl sm:text-3xl font-bold text-white leading-tight'>
                                    {video.title || 'Untitled Video'}
                                </h1>

                                {/* Video Meta Info */}
                                <div className='flex flex-wrap items-center gap-4 mt-3 text-sm text-gray-400'>
                                    <div className='flex items-center gap-2'>
                                        <div className='w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-white font-semibold text-xs'>
                                            {video.User?.firstName?.charAt(0) || 'U'}
                                        </div>
                                        <span className='capitalize font-medium'>
                                            {video.User?.firstName} {video.User?.lastName}
                                        </span>
                                    </div>
                                    <span className='text-gray-500'>•</span>
                                    <span>
                                        {dayAgo === 0 ? 'Today' : `${dayAgo} day${dayAgo > 1 ? 's' : ''} ago`}
                                    </span>
                                    <span className='text-gray-500'>•</span>
                                    <span>{video.views || 0} views</span>
                                </div>
                            </div>

                            {/* Action Buttons */}
                            <div className='flex items-center gap-3'>
                                <CopyLink
                                    videoId={videoId}
                                    variant='outline'
                                    className='rounded-lg bg-[#2A2A2A] border-[#3A3A3A] hover:bg-[#3A3A3A] text-white px-4 py-2 transition-colors'
                                />
                                <RichLink
                                    title={video.title as string}
                                    description={truncateString(video.description as string, 150)}
                                    source={video.source}
                                    id={videoId}
                                />
                                <button className='p-2 rounded-lg bg-[#2A2A2A] border border-[#3A3A3A] hover:bg-[#3A3A3A] transition-colors'>
                                    <Download className='w-5 h-5 text-gray-400' />
                                </button>
                            </div>
                        </div>

                        {/* Description */}
                        {video.description && (
                            <div className='pt-4 border-t border-[#2A2A2A]'>
                                <h3 className='text-lg font-semibold text-white mb-3'>Description</h3>
                                <p className='text-gray-300 leading-relaxed whitespace-pre-wrap'>
                                    {video.description}
                                </p>
                            </div>
                        )}
                    </div>
                </div>

                {/* Sidebar - Right Side */}
                <div className='xl:col-span-1 space-y-4'>
                    <div className='bg-[#1A1A1A] rounded-2xl border border-[#2A2A2A] overflow-hidden'>
                        <TabMenu defaultValue='AI Tools' triggers={['AI Tools', 'Transcript', 'Activity']}>
                            <AiTools
                                videoId={videoId}
                                trial={video.User?.trial ?? false}
                                plan={video.User?.subscription?.plan ?? 'FREE'}
                            />
                            <VideoTranscript transcript={video.summery as string}  />
                            {/* <VideoActivity videoId={videoId} /> */}
                            <Activites 
                            author={video.User?.firstName as string}
                            videoId={videoId}
                            />
                        </TabMenu>
                    </div>
                </div>
            </div>
        </div>
    </div>
  )
}

export default VideoPreview